<script lang="ts">
  let props = $props();
  const formatNet = (net: number) => (net > 0 ? "text-green-500" : net < 0 ? "text-red-500" : "");
  const formatDate = (date: string) =>
    new Date(date).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit"
    });
</script>

<div class="w-full tracking-tighter">
  <div class="flex w-full justify-between gap-2 rounded-md border-white/5 bg-white/5 px-2 py-2.5">
    <h2 class="text-sm leading-none font-medium uppercase">{props.title}</h2>
    <p class="flex items-center text-sm">
      {#if props.date}
        <span class="leading-none">{formatDate(props.date)}</span>
      {/if}
    </p>
  </div>
  <div class="-mx-1.5 w-full overflow-y-auto lg:h-[224px]">
    <table class="w-full table-fixed tracking-tighter">
      <thead class="text-right text-xs uppercase">
        <tr>
          <th class="w-4/8 truncate p-2 text-left leading-none font-medium"></th>
          <th class="w-2/8 truncate p-2 leading-none font-medium">Net</th>
          <th class="w-2/8 truncate py-2 pr-1 pl-2 leading-none font-medium">Change</th>
        </tr>
      </thead>
      <tbody class="text-right text-sm">
        {#each props.items as item}
          <tr>
            <td class="py-1.5 pr-2 pl-3.5 text-left leading-none font-normal">
              <div class="flex items-center gap-1">
                <img
                  src="/icons/{item.logo}"
                  alt={item.name}
                  class="size-4 rounded-xs select-none"
                  width="16"
                  height="16"
                />
                <p>{item.name.toUpperCase()}</p>
              </div>
            </td>
            <td class="px-2 py-1.5 leading-none font-normal {formatNet(item.net)}">{item.net}</td>
            {#if item.changeNet == null}
              <td class="px-2 py-1.5 text-sm leading-none whitespace-nowrap"
                ><svg
                  class="inline-block size-4 animate-spin text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  ><circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle><path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path></svg
                >
              </td>
            {:else}
              <td
                class="py-1.5 pr-1 pl-2 text-right leading-none font-normal {formatNet(
                  item.changeNet
                )}">{item.changeNet > 0 ? "+" : ""}{item.changeNet}</td
              >
            {/if}
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</div>
