<script lang="ts">
  import SockJS from "sockjs-client";
  import { onMount, onDestroy } from "svelte";
  import {
    america,
    europe,
    asia,
    usd,
    emerging,
    bonds,
    metals,
    agricultural,
    energy
  } from "$lib/constants/global-market";
  import ListSimple from "$lib/components/app/list-simple.svelte";

  let americaAvg: any = $state(null);
  let europeAvg: any = $state(null);
  let asiaAvg: any = $state(null);
  let usdAvg: any = $state(null);
  let emergingAvg: any = $state(null);
  let bondsAvg: any = $state(null);
  let metalsAvg: any = $state(null);
  let agriculturalAvg: any = $state(null);
  let energyAvg: any = $state(null);

  let americaData = $state(america);
  let europeData = $state(europe);
  let asiaData = $state(asia);
  let usdData = $state(usd);
  let emergingData = $state(emerging);
  let bondsData = $state(bonds);
  let metalsData = $state(metals);
  let agriculturalData = $state(agricultural);
  let energyData = $state(energy);

  let sock: any;
  let TimeZoneID = 55;
  let isConnected = $state(false);
  let heartbeatTimer: ReturnType<typeof setInterval> | undefined;

  const reconnectInterval = 5000;
  const heartbeatInterval = 25000;

  const startSockJS = () => {
    sock = new SockJS("https://streaming.forexpros.com/echo");

    sock.onopen = () => {
      isConnected = true;
      startHeartbeat();

      const allIDs = [
        ...america,
        ...europe,
        ...asia,
        ...usd,
        ...emerging,
        ...bonds,
        ...metals,
        ...agricultural,
        ...energy
      ].map((item) => item.id);

      allIDs.forEach((id) => {
        if (sock && sock.readyState === SockJS.OPEN) {
          sock.send(
            JSON.stringify({ _event: "subscribe", tzID: TimeZoneID, message: `pid-${id}:` })
          );
        }
      });
    };

    sock.onmessage = (e: { data: string }) => {
      try {
        const data = JSON.parse(e.data);

        if (typeof data === "object" && data !== null) {
          data._event = data._event || "tick";
        }

        if (data._event === "tick" && data.message) {
          streamData(data.message.split("::")[1]);
        }
      } catch (err) {}
    };

    sock.onclose = (event: { reason: any; code: number }) => {
      stopHeartbeat();
      isConnected = false;

      if (event.code !== 1000) {
        reconnectSockJS();
      }
    };

    sock.onerror = (error: any) => {
      if (sock) sock.close();
    };
  };

  const startHeartbeat = () => {
    heartbeatTimer = setInterval(() => {
      if (sock && sock.readyState === SockJS.OPEN) {
        sock.send(JSON.stringify({ _event: "heartbeat", data: "h" }));
      }
    }, heartbeatInterval);
  };

  const stopHeartbeat = () => {
    clearInterval(heartbeatTimer);
  };

  const reconnectSockJS = () => {
    stopHeartbeat();
    sock = null;

    setTimeout(() => {
      startSockJS();
    }, reconnectInterval);
  };

  const streamData = (message: any) => {
    const data = JSON.parse(message);

    if (data) {
      const id = parseInt(data.pid);
      const price = data.last_numeric;
      const day = parseFloat(data.pcp.replace("%", ""));

      const categories = [
        { category: "america", data: americaData },
        { category: "europe", data: europeData },
        { category: "asia", data: asiaData },
        { category: "usd", data: usdData },
        { category: "emerging", data: emergingData },
        { category: "bonds", data: bondsData },
        { category: "metals", data: metalsData },
        { category: "agricultural", data: agriculturalData },
        { category: "energy", data: energyData }
      ];

      categories.forEach(({ category, data }) => {
        const itemIndex = data.findIndex((item: any) => item.id === id);
        if (itemIndex !== -1) {
          updateArray(category, id, price, day);
        }
      });
    }
  };

  const fetchData = async (id: number, category: string, retryCount = 0) => {
    const period = "P1W";
    const interval = "P1D";

    try {
      const res = await fetch(
        `https://api.investing.com/api/financialdata/${id}/historical/chart?period=${period}&interval=${interval}&pointscount=60`
      );

      if (res.ok) {
        const direct = await res.json();
        if (direct && direct.data && direct.data.length >= 2) {
          let prev = direct.data[direct.data.length - 2][4];
          if (id === 1910 && prev > 0 && prev < 0.1) prev = prev * 100;
          const last = direct.data[direct.data.length - 1][4];
          const day = ((last - prev) / prev) * 100;
          updateArray(category, id, last, day);
          return;
        }
      }

      throw new Error("");
    } catch (error) {
      try {
        const response = await fetch(`/api/market?id=${id}&period=${period}&interval=${interval}`);
        const data = await response.json();

        if (response.ok && data?.price != null && data?.day != null) {
          updateArray(category, id, data.price, data.day);
        } else if (retryCount < 10) {
          return fetchData(id, category, retryCount + 1);
        }
      } catch (err) {
        if (retryCount < 10) {
          return fetchData(id, category, retryCount + 1);
        }
      }
    }
  };

  const fetchAllData = async () => {
    const allCategories = [
      { category: "america", data: americaData },
      { category: "europe", data: europeData },
      { category: "asia", data: asiaData },
      { category: "usd", data: usdData },
      { category: "emerging", data: emergingData },
      { category: "bonds", data: bondsData },
      { category: "metals", data: metalsData },
      { category: "agricultural", data: agriculturalData },
      { category: "energy", data: energyData }
    ];

    const fetchPromises = allCategories.map(({ category, data }) => {
      return Promise.all(data.map((item: any) => fetchData(item.id, category)));
    });

    await Promise.all(fetchPromises);
    startSockJS();
  };

  const updateArray = (category: string, id: number, price: number, day: number) => {
    let array;

    if (category === "america") {
      array = changeData(americaData, id, price, day);
      americaData = array;
      americaAvg = calcAvg(americaData);
    } else if (category === "europe") {
      array = changeData(europeData, id, price, day);
      europeData = array;
      europeAvg = calcAvg(europeData);
    } else if (category === "asia") {
      array = changeData(asiaData, id, price, day);
      asiaData = array;
      asiaAvg = calcAvg(asiaData);
    } else if (category === "usd") {
      array = changeData(usdData, id, price, day);
      usdData = array;
      usdAvg = calcAvg(usdData);
    } else if (category === "emerging") {
      array = changeData(emergingData, id, price, day);
      emergingData = array;
      emergingAvg = calcAvg(emergingData);
    } else if (category === "bonds") {
      array = changeData(bondsData, id, price, day);
      bondsData = array;
      bondsAvg = calcAvg(bondsData);
    } else if (category === "metals") {
      array = changeData(metalsData, id, price, day);
      metalsData = array;
      metalsAvg = calcAvg(metalsData);
    } else if (category === "agricultural") {
      array = changeData(agriculturalData, id, price, day);
      agriculturalData = array;
      agriculturalAvg = calcAvg(agriculturalData);
    } else if (category === "energy") {
      array = changeData(energyData, id, price, day);
      energyData = array;
      energyAvg = calcAvg(energyData);
    }
  };

  const changeData = (array: any, id: number, price: number, day: number) => {
    return array.map((item: any) => {
      if (item.id === id) {
        return { ...item, price, day };
      }
      return item;
    });
  };

  const calcAvg = (data: any[]) => {
    if (data.length === 0) return 0;
    const sum = data.reduce((acc, item) => acc + (item.day || 0), 0);
    return sum / data.length;
  };

  onMount(() => {
    fetchAllData();
  });

  onDestroy(() => {
    if (sock && (sock.readyState === SockJS.OPEN || sock.readyState === SockJS.CONNECTING)) {
      sock.close(1000, "Component Unmounted");
    }
    stopHeartbeat();
    sock = null;
  });
</script>

<section class="mx-auto max-w-7xl space-y-5 px-2 py-5">
  <h1 class="text-center text-2xl leading-none font-bold tracking-tighter uppercase">
    Global Market
  </h1>
  <div class="grid grid-cols-1 gap-2 space-x-2 md:grid-cols-2 lg:grid-cols-3">
    <ListSimple average={americaAvg} items={americaData} title="America" />
    <ListSimple average={europeAvg} items={europeData} title="Europe" />
    <ListSimple average={asiaAvg} items={asiaData} title="Asia" />
    <ListSimple average={usdAvg} items={usdData} title="Dollar" />
    <ListSimple average={emergingAvg} items={emergingData} title="Emerging" />
    <ListSimple average={bondsAvg} items={bondsData} title="Bonds Yields" />
    <ListSimple average={metalsAvg} items={metalsData} title="Metals" />
    <ListSimple average={agriculturalAvg} items={agriculturalData} title="Agricultural" />
    <ListSimple average={energyAvg} items={energyData} title="Energy" />
  </div>
</section>
