import type Stripe from "stripe";
import { eq } from "drizzle-orm";
import * as schema from "$lib/server/db/schema";
import { services } from "$lib/server/services";

export async function useStripeCustomer({
  stripeCustomer,
  user
}: {
  stripeCustomer: Stripe.Customer;
  user: {
    id: string;
    name: string;
    email: string;
    emailVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
    image?: string | null;
  };
}): Promise<void> {
  const db = services.db();
  const stripeClient = services.stripe();

  const customersList = await stripeClient.customers.search({
    query: `email~"${stripeCustomer.email}"`,
    limit: 1
  });

  const customer = customersList?.data[0];

  if (customer?.metadata?.userId) {
    await stripeClient.customers.del(stripeCustomer.id);

    await stripeClient.customers.update(customer.id, {
      metadata: {
        userId: user.id
      }
    });

    await db
      .update(schema.user)
      .set({ stripeCustomerId: customer.id })
      .where(eq(schema.user.id, user.id));

    await db
      .update(schema.subscription)
      .set({ referenceId: user.id })
      .where(eq(schema.subscription.stripeCustomerId, customer.id));
  }
}
