<script lang="ts">
  let props = $props();
</script>

<div class="w-full tracking-tighter">
  <div class="w-full flex justify-between gap-2 px-2 py-2.5 rounded-md border-white/5 bg-white/5">
    <h2 class="text-sm font-medium leading-none uppercase">{props.title}</h2>
  </div>

  <ul class="mt-1">
    {#each props.items as item}
      <li class="flex items-center justify-between">
        <a href="{props.path}/{item.id}" class="w-full flex items-center gap-1 hover:bg-white/5 px-2 py-1.5 rounded-md">
          <img src="/icons/{item.logo}" alt={item.name} class="size-4 rounded-xs select-none" width="16" height="16" />
          <h3 class="text-sm leading-none uppercase">{item.name}</h3>
        </a>
      </li>
    {/each}
  </ul>
</div>
