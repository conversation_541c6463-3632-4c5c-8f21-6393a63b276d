import { services } from "$lib/server/services";

export async function useCancelSubscription(user: {
  id: string;
  name: string;
  email: string;
  stripeCustomerId?: string;
}): Promise<void> {
  const stripeClient = services.stripe();

  const subscriptionsList = await stripeClient.subscriptions.list({
    customer: user.stripeCustomerId,
    limit: 1
  });

  const subscription = subscriptionsList?.data[0];

  if (subscription) {
    await stripeClient.subscriptions.cancel(subscription.id);
  }
}
