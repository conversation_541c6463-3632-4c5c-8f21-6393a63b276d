import { render } from "svelte/server";
import Contact from "$lib/emails/contact.svelte";
import MagicLink from "$lib/emails/magic-link.svelte";
import Verification from "$lib/emails/verification.svelte";

export async function magicLinkTemplate(props: { url: string }) {
  const { body } = render(MagicLink, { props });
  return body;
}

export async function verificationTemplate(props: {
  user: { name: string; email: string };
  url: string;
}) {
  const { body } = render(Verification, { props });
  return body;
}

export async function contactTemplate(props: {
  data: { firstname: string; lastname: string; email: string; message: string };
}) {
  const { body } = render(Contact, { props });
  return body;
}
