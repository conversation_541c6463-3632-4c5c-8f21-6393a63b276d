import { json } from "@sveltejs/kit";
import { getDynamicTTL } from "$lib/utils";
import { services } from "$lib/server/services";
import type { RequestHandler } from "@sveltejs/kit";

export const GET: RequestHandler = async ({ url }) => {
  const id = url.searchParams.get("id");

  if (!id) {
    return json({ error: "Missing id" }, { status: 400 });
  }

  const redis = services.redis();
  const cacheKey = `indicators:${id}`;
  const cached = await redis.get(cacheKey);

  if (cached) {
    const parsed = typeof cached === "string" ? JSON.parse(cached) : cached;
    return json(parsed, { status: 200 });
  } else {
    try {
      const response = await fetch(`https://economic-fly.fly.dev/${id}`);
      const data = await response.json();

      if (!response.ok || !data) {
        return json({ error: "Failed to fetch data" }, { status: 400 });
      }

      const ttl = getDynamicTTL("P1D");
      await redis.setex(cacheKey, ttl, JSON.stringify(data));

      return json(data, { status: 200 });
    } catch (error) {
      return json({ error: error }, { status: 500 });
    }
  }
};
