<script lang="ts">
  import HeaderLanding from "$lib/components/landing/header.svelte";
  import FooterLanding from "$lib/components/landing/footer.svelte";
  import HeaderApp from "$lib/components/app/header.svelte";
  import FooterApp from "$lib/components/app/footer.svelte";

  let { children, data } = $props();
  const user = data?.session?.user;
  const subscription = data?.subscription;
</script>

{#if user}
  <HeaderApp {user} {subscription} />
{:else}
  <HeaderLanding />
{/if}

<div
  class="absolute inset-x-0 top-4 -z-10 flex transform-gpu justify-center overflow-hidden blur-3xl"
  aria-hidden="true"
>
  <div
    class="aspect-[1108/632] w-[69.25rem] flex-none bg-gradient-to-r from-emerald-500 to-blue-600 opacity-25"
    style="clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%)"
  ></div>
</div>

<div class="w-full pt-16 pb-6">
  {@render children()}
</div>

{#if user}
  <FooterApp showCookies={false} />
{:else}
  <FooterLanding showCookies={false} />
{/if}
