<script lang="ts">
  import { onMount } from "svelte";
  import { initTrack } from "$lib/utils/track";

  let analyticsEnabled = $state(false);
  let marketingEnabled = $state(false);
  let { showCookies = $bindable() } = $props();

  const acceptAllCookies = () => {
    analyticsEnabled = true;
    marketingEnabled = true;

    localStorage.setItem("cookie-consent", "true");
    localStorage.setItem("cookie-analytics", analyticsEnabled.toString());
    localStorage.setItem("cookie-marketing", marketingEnabled.toString());

    initTrack();

    setTimeout(() => {
      showCookies = false;
    }, 200);
  };

  const acceptSelectedCookies = () => {
    localStorage.setItem("cookie-consent", "true");
    localStorage.setItem("cookie-analytics", analyticsEnabled.toString());
    localStorage.setItem("cookie-marketing", marketingEnabled.toString());

    initTrack();

    showCookies = false;
  };

  const rejectAllCookies = () => {
    analyticsEnabled = false;
    marketingEnabled = false;

    localStorage.setItem("cookie-consent", "true");
    localStorage.setItem("cookie-analytics", analyticsEnabled.toString());
    localStorage.setItem("cookie-marketing", marketingEnabled.toString());

    initTrack();

    setTimeout(() => {
      showCookies = false;
    }, 200);
  };

  onMount(() => {
    const storeConsent = localStorage.getItem("cookie-consent");
    const storeAnalytics = localStorage.getItem("cookie-analytics");
    const storeMarketing = localStorage.getItem("cookie-marketing");

    if (storeAnalytics !== null) analyticsEnabled = storeAnalytics === "true";
    if (storeMarketing !== null) marketingEnabled = storeMarketing === "true";
    if (storeConsent === null) showCookies = true;
  });
</script>

{#if showCookies}
  <div
    class="fixed right-4 bottom-4 left-4 z-50 mx-auto space-y-3 rounded-xl bg-gray-800 p-4 text-white shadow-2xl md:right-4 md:left-auto md:max-w-120"
  >
    <p class="text-sm">
      We use essential and analytics cookies to understand how you use our site and improve your
      experience. You can accept or reject cookies. Visit our <a
        href="/cookies-policy"
        class="text-emerald-500 hover:text-emerald-400">Cookies Policy</a
      > to learn more.
    </p>
    <div class="space-y-3">
      <div class="flex items-center justify-between text-sm">
        <p>
          Necessary Cookies <span
            class="ml-1 inline-flex items-center rounded-md bg-white/5 px-2 py-1 text-xs tracking-tighter text-gray-400 lowercase ring-1 ring-white/5 ring-inset"
            >Required</span
          >
        </p>
        <button
          type="button"
          disabled
          class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-not-allowed rounded-full border-2 border-transparent bg-emerald-600 transition-colors duration-200 ease-in-out disabled:opacity-70"
          role="switch"
          aria-checked="true"
        >
          <span class="sr-only">Necessary Cookies</span>
          <span
            aria-hidden="true"
            class="pointer-events-none inline-block h-5 w-5 translate-x-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
          ></span>
        </button>
      </div>

      <div class="flex items-center justify-between text-sm">
        <p>
          Analytics Cookies <span
            class="ml-1 inline-flex items-center rounded-md bg-white/5 px-2 py-1 text-xs tracking-tighter text-gray-400 lowercase ring-1 ring-white/5 ring-inset"
            >Optional</span
          >
        </p>
        <button
          type="button"
          role="switch"
          aria-checked={analyticsEnabled}
          onclick={() => (analyticsEnabled = !analyticsEnabled)}
          class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:ring-2 focus:ring-emerald-600 focus:ring-offset-2 focus:outline-none {analyticsEnabled
            ? 'bg-emerald-600'
            : 'bg-white/30'}"
        >
          <span class="sr-only">Enable analytics</span>
          <span
            aria-hidden="true"
            class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out {analyticsEnabled
              ? 'translate-x-5'
              : 'translate-x-0'}"
          ></span>
        </button>
      </div>

      <div class="flex items-center justify-between text-sm">
        <p>
          Marketing Cookies <span
            class="ml-1 inline-flex items-center rounded-md bg-white/5 px-2 py-1 text-xs tracking-tighter text-gray-400 lowercase ring-1 ring-white/5 ring-inset"
            >Optional</span
          >
        </p>
        <button
          type="button"
          role="switch"
          aria-checked={marketingEnabled}
          onclick={() => (marketingEnabled = !marketingEnabled)}
          class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:ring-2 focus:ring-emerald-600 focus:ring-offset-2 focus:outline-none {marketingEnabled
            ? 'bg-emerald-600'
            : 'bg-white/30'}"
        >
          <span class="sr-only">Enable marketing</span>
          <span
            aria-hidden="true"
            class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out {marketingEnabled
              ? 'translate-x-5'
              : 'translate-x-0'}"
          ></span>
        </button>
      </div>
    </div>

    <div class="flex justify-end gap-1.5">
      <button
        onclick={acceptAllCookies}
        class="cursor-pointer rounded-md bg-emerald-600 px-4 py-2 text-sm font-medium tracking-tighter hover:bg-emerald-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-500 disabled:pointer-events-none disabled:opacity-50"
        >Accept All</button
      >
      <button
        onclick={acceptSelectedCookies}
        class="cursor-pointer rounded-md bg-emerald-600 px-4 py-2 text-sm font-medium tracking-tighter hover:bg-emerald-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-500 disabled:pointer-events-none disabled:opacity-50"
        >Accept Selected</button
      >
      <button
        onclick={rejectAllCookies}
        class="cursor-pointer rounded-md bg-white/10 px-4 py-2 text-sm font-medium tracking-tighter hover:bg-white/15 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-500 disabled:pointer-events-none disabled:opacity-50"
        >Reject All</button
      >
    </div>
  </div>
{/if}
