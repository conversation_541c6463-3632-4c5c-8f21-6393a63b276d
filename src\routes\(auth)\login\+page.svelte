<script lang="ts">
  import { authClient } from "$lib/auth-client";
  import { LegalLinks } from "$lib/constants/links";
  import { ArrowLeft, Mail, Mailbox } from "lucide-svelte";

  let mailURL = $state("");
  let mailError = $state("");
  let mailSend = $state(false);
  let isLoadingForm = $state(false);
  let loadingProvider = $state<"google" | "facebook" | "microsoft" | "apple" | null>(null);
  const currentYear = new Date().getFullYear();

  async function handleSignIn(provider: "google" | "facebook" | "microsoft" | "apple") {
    loadingProvider = provider;
    const data = await authClient.signIn.social({
      provider: provider,
      callbackURL: "/market-risk"
    });

    if (data) {
      setTimeout(() => {
        loadingProvider = null;
      }, 2000);
    }
  }

  async function handleSubmit(event: Event) {
    event.preventDefault();
    isLoadingForm = true;
    mailError = "";

    const form = event.target as HTMLFormElement;
    const formData = new FormData(form);
    const email = (formData.get("email") as string).trim().toLowerCase();
    const domain = email.split("@")[1];
    const domainMap: Record<string, string> = {
      "gmail.com": "https://mail.google.com",
      "googlemail.com": "https://mail.google.com",
      "outlook.com": "https://outlook.live.com",
      "hotmail.com": "https://outlook.live.com",
      "live.com": "https://outlook.live.com",
      "msn.com": "https://outlook.live.com",
      "icloud.com": "https://icloud.com/mail",
      "me.com": "https://icloud.com/mail",
      "mac.com": "https://icloud.com/mail",
      "yahoo.com": "https://mail.yahoo.com",
      "ymail.com": "https://mail.yahoo.com",
      "myyahoo.com": "https://mail.yahoo.com",
      "aol.com": "https://mail.aol.com",
      "zoho.com": "https://mail.zoho.com",
      "zohomail.eu": "https://mail.zoho.com",
      "yandex.com": "https://mail.yandex.com",
      "gmx.us": "https://gmx.com/mail",
      "gmx.com": "https://gmx.com/mail",
      "gmx.de": "https://gmx.net/mail",
      "orange.fr": "https://mail.orange.fr",
      "bol.com.br": "https://email.bol.uol.com.br",
      "uol.com.br": "https://email.uol.com.br",
      "terra.com.br": "https://mail.terra.com.br",
      "outlook.es": "https://outlook.live.com",
      "hotmail.es": "https://outlook.live.com"
    };

    if (!email) {
      mailError = "Please Enter Your Email.";
      isLoadingForm = false;
      return;
    }

    if (!email.includes("@") || !domain) {
      mailError = "Please Enter a Valid Email.";
      isLoadingForm = false;
      return;
    }

    if (!(domain in domainMap)) {
      mailError = `Email domain "${domain}" is not supported.`;
      isLoadingForm = false;
      return;
    }

    const { data, error } = await authClient.signIn.magicLink({
      email: email,
      callbackURL: "/market-risk"
    });

    if (data && !error) {
      mailSend = true;
      mailURL = domainMap[domain];
    }

    if (error) {
      mailError = error.message ?? "Authentication failed.";
    }

    isLoadingForm = false;
  }
</script>

<section class="flex min-h-full flex-col justify-center space-y-6 px-4 py-12 lg:py-6">
  <div class="relative space-y-2 text-center sm:mx-auto sm:w-full sm:max-w-md">
    <a
      href="/"
      class="absolute top-auto bottom-auto left-0 inline-flex translate-y-0.5 items-center justify-center gap-x-1 rounded-lg border border-transparent px-3 py-2 text-sm font-semibold tracking-tighter text-white uppercase hover:bg-white/10 disabled:pointer-events-none disabled:opacity-80 max-[410px]:-translate-y-[110%] max-md:-translate-x-3"
      ><ArrowLeft class="size-5 flex-shrink-0" /> Back</a
    >
    <img class="mx-auto h-10 w-auto select-none" src="/logo.svg" alt="Macro Edge" />
  </div>

  <div
    class="space-y-4 rounded-lg bg-white/3 px-4 py-6 sm:mx-auto sm:w-full sm:max-w-md sm:rounded-2xl"
  >
    <div class="text-center">
      <h2 class="text-2xl leading-none font-semibold tracking-tighter">Sign In</h2>
      <p class="mt-2 text-sm text-gray-300">Choose your preferred sign in method</p>
    </div>

    <div class="flex flex-col gap-y-2">
      <button
        type="button"
        disabled={loadingProvider === "google"}
        onclick={() => handleSignIn("google")}
        class="text-md inline-flex w-full cursor-pointer items-center justify-center gap-x-2 rounded-lg border border-transparent bg-white/6 px-4 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-white/10 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-80"
      >
        {#if loadingProvider === "google"}
          <svg class="mr-2 size-5 animate-spin text-white" viewBox="0 0 24 24" fill="none">
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            />
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
            />
          </svg>
          <span>Signing in…</span>
        {:else}
          <img src="/social/google.svg" alt="Google" class="size-5 select-none" />
          <span>Sign in with Google</span>
        {/if}
      </button>
      <button
        type="button"
        disabled={loadingProvider === "apple"}
        onclick={() => handleSignIn("apple")}
        class="text-md inline-flex w-full cursor-pointer items-center justify-center gap-x-2 rounded-lg border border-transparent bg-white/6 px-4 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-white/10 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-80"
      >
        {#if loadingProvider === "apple"}
          <svg class="mr-2 size-5 animate-spin text-white" viewBox="0 0 24 24" fill="none">
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            />
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
            />
          </svg>
          <span>Signing in…</span>
        {:else}
          <img src="/social/apple.svg" alt="Apple" class="size-5 select-none" />
          <span>Sign in with Apple</span>
        {/if}
      </button>
      <button
        type="button"
        disabled={loadingProvider === "facebook"}
        onclick={() => handleSignIn("facebook")}
        class="text-md inline-flex w-full cursor-pointer items-center justify-center gap-x-2 rounded-lg border border-transparent bg-white/6 px-4 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-white/10 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-80"
      >
        {#if loadingProvider === "facebook"}
          <svg class="mr-2 size-5 animate-spin text-white" viewBox="0 0 24 24" fill="none">
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            />
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
            />
          </svg>
          <span>Signing in…</span>
        {:else}
          <img src="/social/facebook.svg" alt="Facebook" class="size-5 select-none" />
          <span>Sign in with Facebook</span>
        {/if}
      </button>
      <button
        type="button"
        disabled={loadingProvider === "microsoft"}
        onclick={() => handleSignIn("microsoft")}
        class="text-md inline-flex w-full cursor-pointer items-center justify-center gap-x-2 rounded-lg border border-transparent bg-white/6 px-4 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-white/10 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-80"
      >
        {#if loadingProvider === "microsoft"}
          <svg class="mr-2 size-5 animate-spin text-white" viewBox="0 0 24 24" fill="none">
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            />
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
            />
          </svg>
          <span>Signing in…</span>
        {:else}
          <img src="/social/microsoft.svg" alt="Microsoft" class="size-5 select-none" />
          <span>Sign in with Microsoft</span>
        {/if}
      </button>
    </div>

    <div class="relative w-full">
      <div
        class="relative flex items-center justify-center text-xs leading-6 font-medium uppercase"
      >
        <span class="absolute top-auto bottom-auto left-0 w-3/10 border-t border-white/30"></span>
        <span class="absolute top-auto right-0 bottom-auto w-3/10 border-t border-white/30"></span>
        <span>Or continue with</span>
      </div>
    </div>

    {#if mailSend}
      <div class="flex flex-col items-center space-y-1 text-center">
        <a
          href={mailURL}
          target="_blank"
          class="text-md inline-flex w-full max-w-45 cursor-pointer items-center justify-center gap-x-2 rounded-lg border border-transparent bg-emerald-600 px-4 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-emerald-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-80"
        >
          <Mailbox class="size-5.5 select-none" />
          <span>Open Mailbox</span>
        </a>
        <p class="text-md tracking-tighter">Check your inbox for a link to sign in.</p>

        <p class="text-xs text-gray-300">
          Didn&rsquo;t get the email? Check your spam or junk folder
        </p>
      </div>
    {:else}
      <form action="/" method="POST" onsubmit={handleSubmit} class="flex flex-col gap-y-2">
        <div class="flex flex-col gap-y-2">
          <input
            type="email"
            name="email"
            id="email"
            autocomplete="on"
            placeholder="Email Address"
            class="block w-full rounded-md border-0 bg-white/5 px-3.5 py-2 text-white shadow-sm ring-1 ring-transparent ring-inset focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white sm:text-sm sm:leading-6"
            required
          />
        </div>
        <button
          type="submit"
          class="text-md inline-flex w-full cursor-pointer items-center justify-center gap-x-2 rounded-lg border border-transparent bg-white/6 px-4 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-white/10 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-90"
          disabled={isLoadingForm}
        >
          {#if isLoadingForm}
            <svg class="mr-2 size-5 animate-spin text-white" viewBox="0 0 24 24" fill="none">
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              />
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
              />
            </svg>
            <span>Sending…</span>
          {:else}
            <Mail class="size-5 select-none" />
            <span>Send Magic Link</span>
          {/if}
        </button>
        {#if mailError}
          <p class="text-center text-sm text-red-500">{mailError}</p>
        {/if}
      </form>
    {/if}
    <p class="text-center text-sm">
      By continuing, you agree to our <br />
      <a href={LegalLinks[0].route} class="text-emerald-500 hover:text-emerald-400"
        >{LegalLinks[0].label}</a
      >
      &
      <a href={LegalLinks[1].route} class="text-emerald-500 hover:text-emerald-400"
        >{LegalLinks[1].label}</a
      >.
    </p>
  </div>
  <p class="text-center text-sm text-gray-400">© {currentYear} Macro Edge, All rights reserved.</p>
</section>

<div
  class="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl"
  aria-hidden="true"
>
  <div
    class="mx-auto aspect-1155/678 w-[72.1875rem] bg-linear-to-tr from-emerald-500 to-blue-600 opacity-30"
    style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
  ></div>
</div>

<style>
  :global(html),
  :global(body) {
    height: 100%;
  }
</style>
