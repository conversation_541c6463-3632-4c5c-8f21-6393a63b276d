import type { <PERSON><PERSON> } from "@sveltejs/kit";
import { json, text } from "@sveltejs/kit";

export function csrf(allowedPaths: string[], allowedOrigins: string[] = []): Handle {
  return async ({ event, resolve }) => {
    const { request, url } = event;

    // Get the 'origin' header from the incoming request
    const requestOrigin = request.headers.get("origin");

    // Determine if the request comes from the same origin
    const isSameOrigin = requestOrigin === url.origin;

    // Check if the request origin is explicitly allowed (trusted external origins)
    const isAllowedOrigin = allowedOrigins.includes(requestOrigin ?? "");

    // Define conditions under which the request is forbidden (potential CSRF attack)
    const forbidden =
      isFormContentType(request) && // Checks if the request contains form data
      ["POST", "PUT", "PATCH", "DELETE"].includes(request.method) && // State-changing methods
      !isSameOrigin && // Origin mismatch
      !isAllowedOrigin && // Not explicitly allowed
      !allowedPaths.includes(url.pathname); // Path not explicitly allowed

    // If forbidden, return a 403 Forbidden response immediately
    if (forbidden) {
      const message = `Cross-site ${request.method} form submissions are forbidden`;

      // Return JSON or plain text based on request headers
      if (request.headers.get("accept") === "application/json") {
        return json({ message }, { status: 403 });
      }
      return text(message, { status: 403 });
    }

    // If the request passes CSRF checks, continue to the next middleware or endpoint
    return resolve(event);
  };

  /**
   * Helper function to check if request 'origin' is allowed.
   */
  function isAllowedOrigin(requestOrigin: string | null, allowedOrigins: string[]) {
    return allowedOrigins.includes(requestOrigin ?? "");
  }

  /**
   * Helper function to determine if request content-type indicates a form submission
   */
  function isFormContentType(request: Request) {
    const type = request.headers.get("content-type")?.split(";", 1)[0].trim().toLowerCase() ?? "";
    return ["application/x-www-form-urlencoded", "multipart/form-data", "text/plain"].includes(
      type
    );
  }
}
