import { createAuthClient } from "better-auth/svelte";
import { stripeClient } from "@better-auth/stripe/client";
import { magicLinkClient } from "better-auth/client/plugins";
import { multiSessionClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
  plugins: [
    multiSessionClient(),
    magicLinkClient(),
    stripeClient({
      subscription: true
    })
  ]
});
