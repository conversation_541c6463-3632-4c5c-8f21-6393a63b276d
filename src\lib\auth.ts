import { betterAuth } from "better-auth";
import { stripe } from "@better-auth/stripe";
import { magicLink } from "better-auth/plugins";
import { multiSession } from "better-auth/plugins";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { useRateLimit } from "$lib/server/hooks/useRateLimit";
import { useStripeCustomer } from "$lib/server/hooks/useStripeCustomer";
import { magicLinkTemplate, verificationTemplate } from "$lib/server/email";
import { useCancelSubscription } from "$lib/server/hooks/useCancelSubscription";
import { services } from "$lib/server/services";
import { env } from "$env/dynamic/private";
import { lazy } from "$lib/utils";

export function getAuth() {
  return betterAuth({
    appName: "macro-edge",
    baseURL: env.BASE_URL!,
    database: drizzleAdapter(services.db(), { provider: "pg" }),
    plugins: [
      multiSession({
        maximumSessions: 5
      }),
      magicLink({
        sendMagicLink: async ({ email, url }, request) => {
          const ipAddress =
            request?.headers.get("x-forwarded-for")?.split(",")[0]?.trim() ||
            request?.headers.get("x-real-ip") ||
            request?.headers.get("cf-connecting-ip") ||
            "Unknown";
          const domain = email.split("@")[1]?.toLowerCase();
          const domainMap: Record<string, string> = {
            "gmail.com": "https://mail.google.com",
            "googlemail.com": "https://mail.google.com",
            "outlook.com": "https://outlook.live.com",
            "hotmail.com": "https://outlook.live.com",
            "live.com": "https://outlook.live.com",
            "msn.com": "https://outlook.live.com",
            "icloud.com": "https://icloud.com/mail",
            "me.com": "https://icloud.com/mail",
            "mac.com": "https://icloud.com/mail",
            "yahoo.com": "https://mail.yahoo.com",
            "ymail.com": "https://mail.yahoo.com",
            "myyahoo.com": "https://mail.yahoo.com",
            "aol.com": "https://mail.aol.com",
            "zoho.com": "https://mail.zoho.com",
            "zohomail.eu": "https://mail.zoho.com",
            "yandex.com": "https://mail.yandex.com",
            "gmx.us": "https://gmx.com/mail",
            "gmx.com": "https://gmx.com/mail",
            "gmx.de": "https://gmx.net/mail",
            "orange.fr": "https://mail.orange.fr",
            "bol.com.br": "https://email.bol.uol.com.br",
            "uol.com.br": "https://email.uol.com.br",
            "terra.com.br": "https://mail.terra.com.br",
            "outlook.es": "https://outlook.live.com",
            "hotmail.es": "https://outlook.live.com"
          };

          if (!email.includes("@") || !domain) {
            throw new Error("Please enter a valid email.");
          }

          if (!(domain in domainMap)) {
            throw new Error(`Email domain "${domain}" is not supported.`);
          }

          if (
            ipAddress !== "Unknown" &&
            (await useRateLimit(`magiclink:${ipAddress}`, 3, 60 * 60 * 1))
          ) {
            throw new Error(`You have reached the hourly limit`);
          }

          const html = await magicLinkTemplate({ url });
          const text = `Sign in to Macro Edge\n\nClick the link below to log into your account:\n${url}\n\nIf you didn&rsquo;t request this, you can ignore this email.`;

          const resend = services.resend();
          const { error } = await resend.emails.send({
            from: "Macro Edge <<EMAIL>>",
            to: email,
            subject: "Sign in to Macro Edge",
            html,
            text
          });

          if (error) {
            throw new Error("Failed to send magic link. Please try again.");
          }
        }
      }),
      stripe({
        createCustomerOnSignUp: true,
        stripeClient: services.stripe(),
        stripeWebhookSecret: env.STRIPE_WEBHOOK_SECRET!,
        onCustomerCreate: async ({ stripeCustomer, user }) => {
          useStripeCustomer({ stripeCustomer, user });
        },
        subscription: {
          enabled: true,
          plans: [
            {
              name: "pro",
              priceId: env.STRIPE_PRICE_ID!
            },
            {
              name: "pro-trial",
              priceId: env.STRIPE_PRICE_ID!,
              freeTrial: {
                days: 7
              }
            }
          ]
        }
      })
    ],
    emailVerification: {
      sendOnSignUp: true,
      autoSignInAfterVerification: true,
      sendVerificationEmail: async ({ user, url }) => {
        const domain = user.email.split("@")[1]?.toLowerCase();
        const domainMap: Record<string, string> = {
          "gmail.com": "https://mail.google.com",
          "googlemail.com": "https://mail.google.com",
          "outlook.com": "https://outlook.live.com",
          "hotmail.com": "https://outlook.live.com",
          "live.com": "https://outlook.live.com",
          "msn.com": "https://outlook.live.com",
          "icloud.com": "https://icloud.com/mail",
          "me.com": "https://icloud.com/mail",
          "mac.com": "https://icloud.com/mail",
          "yahoo.com": "https://mail.yahoo.com",
          "ymail.com": "https://mail.yahoo.com",
          "myyahoo.com": "https://mail.yahoo.com",
          "aol.com": "https://mail.aol.com",
          "zoho.com": "https://mail.zoho.com",
          "zohomail.eu": "https://mail.zoho.com",
          "yandex.com": "https://mail.yandex.com",
          "gmx.us": "https://gmx.com/mail",
          "gmx.com": "https://gmx.com/mail",
          "gmx.de": "https://gmx.net/mail",
          "orange.fr": "https://mail.orange.fr",
          "bol.com.br": "https://email.bol.uol.com.br",
          "uol.com.br": "https://email.uol.com.br",
          "terra.com.br": "https://mail.terra.com.br",
          "outlook.es": "https://outlook.live.com",
          "hotmail.es": "https://outlook.live.com"
        };

        if (!user.email.includes("@") || !domain) {
          throw new Error("Please enter a valid email.");
        }

        if (!(domain in domainMap)) {
          throw new Error(`Email domain "${domain}" is not supported.`);
        }

        const html = await verificationTemplate({ user, url });
        const text = `Hi ${user.name || "there"},\n\nWelcome to Macro Edge!\nPlease verify your email to complete your signup process.\nClick the link below to verify your account: ${url}\nIf you didn't request this, feel free to ignore this email.\n\nBest, Macro Edge.`;

        const resend = services.resend();
        const { error } = await resend.emails.send({
          from: "Macro Edge <<EMAIL>>",
          to: user.email,
          subject: "Confirm your email to activate your Macro Edge account",
          html,
          text
        });

        if (error) {
          throw new Error("Failed to verification link. Please try again.");
        }
      }
    },
    user: {
      changeEmail: {
        enabled: false
      },
      deleteUser: {
        enabled: false,
        afterDelete: async (user) => {
          useCancelSubscription(user);
        }
      }
    },
    emailAndPassword: {
      enabled: false
    },
    account: {
      accountLinking: {
        enabled: true,
        allowUnlinkingAll: false,
        allowDifferentEmails: false,
        trustedProviders: ["google", "apple", "facebook", "microsoft"]
      }
    },
    socialProviders: {
      google: {
        clientId: env.GOOGLE_CLIENT_ID!,
        clientSecret: env.GOOGLE_CLIENT_SECRET!
      },
      facebook: {
        clientId: env.FACEBOOK_CLIENT_ID!,
        clientSecret: env.FACEBOOK_CLIENT_SECRET!
      },
      microsoft: {
        clientId: env.MICROSOFT_CLIENT_ID!,
        clientSecret: env.MICROSOFT_CLIENT_SECRET!,
        tenantId: "common",
        requireSelectAccount: true
      },
      apple: {
        clientId: env.APPLE_CLIENT_ID!,
        clientSecret: env.APPLE_CLIENT_SECRET!,
        appBundleIdentifier: env.APPLE_APP_BUNDLE_IDENTIFIER!
      }
    },
    trustedOrigins: ["https://appleid.apple.com"]
  });
}

export const auth = lazy(() => getAuth());
