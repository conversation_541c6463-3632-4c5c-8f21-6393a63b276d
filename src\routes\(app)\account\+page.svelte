<script lang="ts">
  import { onMount } from "svelte";
  import { goto } from "$app/navigation";
  import { authClient } from "$lib/auth-client";
  import {
    Check,
    Info,
    X,
    Wallet,
    LogIn,
    LogOut,
    TriangleAlert,
    Trash2,
    Laptop,
    Smartphone
  } from "lucide-svelte";

  type Provider = "google" | "facebook" | "microsoft" | "apple";
  const providers: Provider[] = ["google", "apple", "facebook", "microsoft"];

  let { data } = $props();
  let user = $state(data?.session?.user);
  let subscription = $state(data?.subscription);
  let activeSession = $state(data?.session?.session);
  let listAccounts = $state<Provider[]>([]);
  let loadingProvider = $state<Provider | null>(null);
  let listSessions = $state<any[]>([]);
  let loggingOutToken = $state<string | null>(null);
  let loadingSubscription = $state<boolean>(false);
  let loadingBilling = $state<boolean>(false);
  let loadingDelete = $state<boolean>(false);
  let showDeleteDialog = $state(false);
  let confirmDeleteInput = $state("");

  const today = new Date();
  const trialEnd = subscription?.trialEnd ? new Date(subscription.trialEnd) : null;
  const periodEnd = subscription?.periodEnd ? new Date(subscription.periodEnd) : null;
  const endDate = trialEnd ?? periodEnd;
  const daysRemaining = endDate
    ? Math.max(0, Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)))
    : null;

  onMount(async () => {
    const accounts = await authClient.listAccounts();

    if (accounts.data) {
      listAccounts = accounts.data?.map((account) => account.provider as Provider) ?? [];
    }

    const sessions = await authClient.listSessions();

    if (sessions.data) {
      listSessions = sessions.data?.map((session) => session) ?? [];
    }

    const handleKeydown = (event: { key: string }) => {
      if (event.key === "Escape") {
        showDeleteDialog = false;
        confirmDeleteInput = "";
      }
    };

    window.addEventListener("keydown", handleKeydown);
  });

  async function handleCancelSubscription() {
    loadingSubscription = true;

    const data = await authClient.subscription.cancel({
      returnUrl: "/account"
    });

    setTimeout(() => {
      loadingSubscription = false;
    }, 2000);
  }

  async function handleRestoreSubscription() {
    loadingSubscription = true;

    if (subscription?.status === "active") {
      const data = await authClient.subscription.restore();
      const url = window.location.href;

      if (data) window.location.href = url;
    } else if (subscription?.status === "trialing") {
      const res = await fetch("/api/billing", { method: "POST" });
      const data = await res.json();

      if (data?.url) window.location.href = data.url;
    }

    setTimeout(() => {
      loadingSubscription = false;
    }, 2000);
  }

  async function handleSubscription() {
    loadingSubscription = true;

    const data = await authClient.subscription.upgrade({
      plan: "pro",
      cancelUrl: "/account",
      successUrl: "/market-risk"
    });

    setTimeout(() => {
      loadingSubscription = false;
    }, 2000);
  }

  async function handleBilling() {
    loadingBilling = true;

    const res = await fetch("/api/billing", {
      method: "POST"
    });

    const data = await res.json();

    if (data?.url) window.location.href = data.url;

    setTimeout(() => {
      loadingBilling = false;
    }, 2000);
  }

  async function linkAccount(provider: Provider) {
    loadingProvider = provider;

    const data = await authClient.linkSocial({
      provider,
      callbackURL: "/market-risk"
    });

    setTimeout(() => {
      loadingProvider = null;
    }, 2000);
  }

  async function unlinkAccount(providerId: Provider) {
    loadingProvider = providerId;

    const data = await authClient.unlinkAccount({ providerId });

    listAccounts = listAccounts.filter((id) => id !== providerId);

    setTimeout(() => {
      loadingProvider = null;
    }, 2000);
  }

  async function handleLogout() {
    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          goto("/");
        }
      }
    });
  }

  async function handleLogOutSession(token: string) {
    loggingOutToken = token;

    if (token === activeSession.token) {
      handleLogout();
    } else {
      const data = await authClient.revokeSession({ token });

      listSessions = listSessions.filter((session) => session.token !== token);
    }

    setTimeout(() => {
      loggingOutToken = null;
    }, 2000);
  }

  // async function handleDeleteAccount() {
  //   loadingDelete = true;

  //   const data = await authClient.deleteUser();

  //   if (data) {
  //     goto("/");
  //   }
  // }

  function getDeviceIcon(userAgent: string) {
    const ua = userAgent.toLowerCase();
    if (ua.includes("mobile") || ua.includes("android") || ua.includes("iphone")) {
      return Smartphone;
    }

    return Laptop;
  }

  function getDeviceInfo(userAgent: string) {
    const ua = userAgent.toLowerCase();
    let os = "Unknown";
    let browser = "Unknown";

    if (ua.includes("windows")) {
      os = "Windows";
    } else if (ua.includes("mac os")) {
      os = "macOS";
    } else if (ua.includes("linux")) {
      os = "Linux";
    } else if (ua.includes("android")) {
      os = "Android";
    } else if (ua.includes("iphone") || ua.includes("ipad")) {
      os = "iOS";
    }

    if (ua.includes("firefox")) {
      browser = "Firefox";
    } else if (ua.includes("chrome")) {
      browser = "Chrome";
    } else if (ua.includes("safari")) {
      browser = "Safari";
    } else if (ua.includes("edge")) {
      browser = "Edge";
    } else if (ua.includes("opera")) {
      browser = "Opera";
    }

    return {
      os,
      browser
    };
  }
</script>

<section class="mx-auto max-w-7xl space-y-5 px-2 py-5">
  <h1 class="text-center text-2xl leading-none font-bold tracking-tighter uppercase">Account</h1>
  <div class="divide-y divide-white/10 rounded-md bg-white/3 tracking-tighter">
    <div class="space-y-3 p-4">
      <div>
        <h2 class="text-xl font-semibold">Profile</h2>
        <p class="text-md text-gray-300">Profile information.</p>
      </div>

      <div class="flex items-center">
        <div class="shrink-0">
          <img
            class="size-15 rounded-full select-none"
            src={user.image || "/user.png"}
            alt={user.name}
          />
        </div>

        <div class="ml-3 text-left">
          <div class="text-lg font-medium tracking-tighter">{user.name}</div>
          <div class="text-sm text-gray-300">{user.email}</div>
        </div>
      </div>
    </div>

    <div class="space-y-3 p-4">
      <div>
        <h2 class="text-xl font-semibold">Billing</h2>
        <p class="text-md text-gray-300">Billing information.</p>
      </div>

      <div class="space-y-3 rounded-lg bg-white/5 p-4">
        <div class="flex flex-row items-center justify-between gap-4">
          <div>
            <h3 class="flex items-center gap-2 text-lg font-semibold uppercase">
              Pro <span
                class="rounded-md px-2 py-1 text-center text-xs leading-none font-medium uppercase ring-1 ring-inset {subscription?.status ===
                'trialing'
                  ? `bg-yellow-500/20 text-yellow-500 ring-yellow-500/20`
                  : subscription?.status === 'active'
                    ? `bg-emerald-500/20 text-emerald-500 ring-emerald-500/20`
                    : `bg-red-500/20 text-red-500 ring-red-500/20`}"
              >
                {subscription?.status === "trialing"
                  ? "Trial"
                  : subscription?.status === "active"
                    ? "Active"
                    : "Cancelled"}
              </span>
            </h3>
            <p class="text-sm text-gray-300">
              Period end on <span class="font-semibold">
                {subscription?.trialEnd?.toLocaleDateString("en-UK") ??
                  subscription?.periodEnd?.toLocaleDateString("en-UK") ??
                  "N/A"}</span
              >
            </p>
          </div>

          <div class="flex items-center">
            <span class="text-2xl font-bold">$20</span><span class="ml-1 text-gray-300">/month</span
            >
          </div>
        </div>

        <div class="relative w-full">
          <div
            class="flex items-center gap-2 rounded-md p-3 {subscription?.status === 'trialing'
              ? `bg-yellow-500/20 text-yellow-500`
              : subscription?.status === 'active'
                ? `bg-emerald-500/20 text-emerald-500`
                : `bg-red-500/20 text-red-500`}"
          >
            <Info class="size-6 shrink-0 select-none" />
            <p class="text-sm">
              {#if subscription?.status === "cancelled"}
                Subscription is cancelled.
              {:else}
                {subscription?.status === "trialing"
                  ? "Trial"
                  : subscription?.status === "active"
                    ? "Active"
                    : ""} Period:
                <span class="font-medium"> {daysRemaining} days remaining </span>
              {/if}
            </p>
          </div>
          <ul role="list" class="text-md mt-6 space-y-2 text-gray-300">
            <li class="flex gap-x-2">
              <Check class="w-5 flex-none text-emerald-500" /> Full access to all features
            </li>
            <li class="flex gap-x-2">
              <Check class="w-5 flex-none text-emerald-500" /> Algo trading predictions
            </li>
            <li class="flex gap-x-2">
              <Check class="w-5 flex-none text-emerald-500" /> Market risk analysis & sentiment tracking
            </li>
            <li class="flex gap-x-2">
              <Check class="w-5 flex-none text-emerald-500" /> Real-time macroeconomic data
            </li>
            <li class="flex gap-x-2">
              <Check class="w-5 flex-none text-emerald-500" /> Institutional COT reports
            </li>
            <li class="flex gap-x-2">
              <Check class="w-5 flex-none text-emerald-500" /> Economic calendar & event tracking
            </li>
          </ul>
        </div>
      </div>

      <div class="flex gap-2">
        {#if subscription?.status === "trialing" || subscription?.status === "active"}
          {#if subscription?.cancelAtPeriodEnd && daysRemaining && daysRemaining > 0}
            <button
              type="submit"
              disabled={loadingSubscription}
              onclick={() => handleRestoreSubscription()}
              class="text-md inline-flex w-1/2 cursor-pointer items-center justify-center gap-1 rounded-lg border border-transparent bg-emerald-600 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-emerald-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-90 sm:w-auto sm:min-w-46"
              ><LogIn class="size-5 select-none" />
              {loadingSubscription ? "Loading..." : "Restore Subscription"}</button
            >
          {:else}
            <button
              type="submit"
              disabled={loadingSubscription}
              onclick={() => handleCancelSubscription()}
              class="text-md inline-flex w-1/2 cursor-pointer items-center justify-center gap-0 rounded-lg border border-transparent bg-red-700 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-red-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-90 sm:w-auto sm:min-w-46"
              ><X class="size-5 select-none" />
              {loadingSubscription ? "Loading..." : "Cancel Subscription"}</button
            >
          {/if}
        {:else}
          <button
            type="submit"
            disabled={loadingSubscription}
            onclick={() => handleSubscription()}
            class="text-md inline-flex w-1/2 cursor-pointer items-center justify-center gap-1 rounded-lg border border-transparent bg-emerald-600 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-emerald-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-90 sm:w-auto sm:min-w-46"
            ><LogIn class="size-5 select-none" />
            {loadingSubscription ? "Loading..." : "Subscribe"}</button
          >
        {/if}
        <button
          type="submit"
          disabled={loadingBilling}
          onclick={() => handleBilling()}
          class="text-md inline-flex w-1/2 cursor-pointer items-center justify-center gap-1 rounded-lg border border-transparent bg-white/15 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-white/20 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-90 sm:w-auto sm:min-w-46"
          ><Wallet class="size-5 select-none" />
          {loadingBilling ? "Loading..." : "Billing Portal"}</button
        >
      </div>
    </div>

    <div class="space-y-3 p-4">
      <div>
        <h2 class="text-xl font-semibold">Connected Accounts</h2>
        <p class="text-md text-gray-300">Link your accounts to enable social login.</p>
      </div>

      <ul class="space-y-2">
        {#each providers as provider}
          <li class="flex items-center justify-between rounded-lg bg-white/5 p-4">
            <div class="flex items-center gap-3">
              <img src={`/social/${provider}.svg`} alt={provider} class="size-6 select-none" />
              <span class="text-lg leading-none font-medium capitalize">{provider}</span>
            </div>

            <div>
              {#if listAccounts.includes(provider)}
                <button
                  type="submit"
                  onclick={() => unlinkAccount(provider)}
                  disabled={loadingProvider === provider || listAccounts.length === 1}
                  class="text-md inline-flex cursor-pointer items-center justify-center gap-1 rounded-lg border border-transparent bg-white/15 px-4 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-white/20 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-90"
                  ><LogOut class="size-5 select-none" />
                  {loadingProvider === provider ? "Unlinking..." : "Unlink"}</button
                >
              {:else if listAccounts.length > 0}
                <button
                  type="submit"
                  onclick={() => linkAccount(provider)}
                  disabled={loadingProvider === provider}
                  class="text-md inline-flex cursor-pointer items-center justify-center gap-1 rounded-lg border border-transparent bg-emerald-600 px-4 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-emerald-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-90"
                  ><LogIn class="size-5 select-none" />
                  {loadingProvider === provider ? "Linking..." : "Link"}</button
                >
              {/if}
            </div>
          </li>
        {/each}
      </ul>
    </div>

    <div class="space-y-3 p-4">
      <div>
        <h2 class="text-xl font-semibold">Active Sessions</h2>
        <p class="text-md text-gray-300">These are devices that have logged into your account.</p>
      </div>

      <ul class="space-y-2">
        {#each listSessions as session}
          {@const Icon = getDeviceIcon(session.userAgent)}
          <li class="flex items-center justify-between rounded-lg bg-white/5 p-4">
            <div class="flex items-center gap-3">
              <p class="text-md flex items-center gap-2 leading-none">
                <Icon class="w-6 select-none" />
                {getDeviceInfo(session.userAgent).os}, {getDeviceInfo(session.userAgent).browser}, {session.ipAddress}
              </p>
            </div>

            <div>
              <button
                type="submit"
                disabled={loggingOutToken === session.token}
                onclick={() => handleLogOutSession(session.token)}
                class="text-md inline-flex cursor-pointer items-center justify-center gap-1 rounded-lg border border-transparent bg-red-700 px-4 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-red-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-90"
                ><LogOut class="size-5 select-none" />
                {#if loggingOutToken === session.token}
                  Terminating...
                {:else if session.token === activeSession.token}
                  Log Out
                {:else}
                  Terminate
                {/if}
              </button>
            </div>
          </li>
        {/each}
      </ul>
    </div>
    <!--
    <div class="space-y-3 p-4">
      <div>
        <h2 class="text-xl font-semibold">Danger Zone</h2>
        <p class="text-md text-gray-300">
          Deleting your account is irreversible. All your data will be permanently removed.
        </p>
      </div>

      <div class="flex items-center gap-2 rounded-md bg-red-600/20 p-3">
        <TriangleAlert class="size-6 shrink-0 text-red-500 select-none" />
        <p class="text-base text-red-400">
          <span class="text-base font-medium">Delete Account</span><br />
          <span
            >Deleting your account is permanent and cannot be undone.<br /> All your data, including
            profile information, subscription active, and connected providers will be permanently removed.</span
          >
        </p>
      </div>

      <div class="flex gap-2">
        <button
          type="submit"
          onclick={() => (showDeleteDialog = true)}
          class="text-md inline-flex w-full cursor-pointer items-center justify-center gap-1 rounded-lg border border-transparent bg-red-700 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-red-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-90 sm:w-auto sm:min-w-46"
          ><Trash2 class="size-5 select-none" /> Delete Account</button
        >
      </div>
    </div>
  </div> -->
  </div>
</section>
<!--
{#if showDeleteDialog}
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-gray-950/50 backdrop-blur-sm">
    <div class="relative w-full max-w-md space-y-5 rounded-xl bg-white/5 p-6">
      <X
        class="absolute top-5 right-5 size-7 cursor-pointer text-white/75 hover:text-white"
        onclick={() => {
          showDeleteDialog = false;
          confirmDeleteInput = "";
        }}
      />
      <h3 class="text-xl font-semibold tracking-tighter text-white">Delete Account</h3>
      <p>
        This action is <strong>permanent</strong>. Type
        <code class="bg-red-600 px-1 py-0.5 text-sm">delete</code> below to confirm.
      </p>

      <input
        type="text"
        bind:value={confirmDeleteInput}
        class="w-full rounded-md border-2 border-gray-300 px-3 py-2 focus:ring-2 focus:ring-gray-300 focus:outline-none"
        placeholder="Type `delete` to confirm"
      />
      <div class="flex justify-end gap-2">
        <button
          class="cursor-pointer rounded-md bg-white/10 px-4 py-2 font-medium text-white shadow hover:bg-white/20"
          onclick={() => {
            showDeleteDialog = false;
            confirmDeleteInput = "";
          }}
        >
          Cancel
        </button>

        <button
          onclick={() => {
            handleDeleteAccount();
          }}
          disabled={confirmDeleteInput.toLowerCase() !== "delete" || loadingDelete}
          class="cursor-pointer rounded-md bg-red-600 px-4 py-2 font-medium text-white shadow hover:bg-red-700 disabled:pointer-events-none disabled:opacity-50"
        >
          {loadingDelete ? "Deleting..." : "Confirm Delete"}
        </button>
      </div>
    </div>
  </div>
{/if} -->
