import {
  House,
  Goal,
  <PERSON>ha<PERSON>,
  Gem,
  CircleHelp,
  Mail,
  Activity,
  Globe,
  CalendarDays,
  TrendingUp,
  FileChartColumnIncreasing,
  LifeBuoy,
  CircleUserRound,
  LogOut,
  Instagram,
  Facebook,
  Youtube
} from "lucide-svelte";

export const HeaderLinks = [
  { route: "/", label: "Home", icon: House },
  { route: "#why-us", label: "Why Macro Edge", icon: Goal },
  { route: "#features", label: "Features", icon: Shapes },
  { route: "#pricing", label: "Pricing", icon: Gem },
  { route: "#faqs", label: "FAQs", icon: CircleHelp },
  { route: "#contact", label: "Contact", icon: Mail }
];

export const SocialLinks = [
  { route: "mailto:<EMAIL>", label: "Email", icon: Mail },
  { route: "https://www.instagram.com/macroedgepro", label: "Instagram", icon: Instagram },
  { route: "https://www.facebook.com/macroedgepro", label: "Facebook", icon: Facebook },
  { route: "https://www.youtube.com/@macroedgepro", label: "Youtube", icon: Youtube }
];

export const LegalLinks = [
  { route: "/terms-policy", label: "Terms of Service", icon: null },
  { route: "/privacy-policy", label: "Privacy Policy", icon: null },
  { route: "/cookies-policy", label: "Cookies Policy", icon: null }
];

export const HeaderLinksApp = [
  { route: "/market-risk", label: "Market Risk", icon: Activity },
  { route: "/global-market", label: "Global Market", icon: Globe },
  { route: "/economic-calendar", label: "Economic Calendar", icon: CalendarDays },
  { route: "/economic-indicators", label: "Economic Indicators", icon: TrendingUp },
  { route: "/cot-report", label: "COT Report", icon: FileChartColumnIncreasing },
  { route: "/support", label: "Support", icon: LifeBuoy }
];

export const UserLinksApp = [
  { route: "/account", label: "Account", icon: CircleUserRound },
  { route: "/account/logout", label: "Logout", icon: LogOut }
];
