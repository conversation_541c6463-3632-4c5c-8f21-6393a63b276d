<script lang="ts">
  import { onMount } from "svelte";
  import { enhance } from "$app/forms";
  import { authClient } from "$lib/auth-client";
  import { ArrowLeft, Pencil, Check } from "lucide-svelte";

  let { data, form } = $props();
  let isLoadingForm = $state(false);
  let fullName = $state(data.session.user.name);
  const currentYear = new Date().getFullYear();

  onMount(() => {
    if (data.session.user.name && !data.subscription) {
      setTimeout(() => {
        handleSubscription();
      }, 2000);
    }
  });

  async function handleSubscription() {
    await authClient.subscription.upgrade({
      plan: "pro-trial",
      cancelUrl: "/account",
      successUrl: "/market-risk"
    });
  }

  async function handleSubmit() {
    isLoadingForm = true;

    return async ({ update, result }: { update: () => Promise<void>; result: any }) => {
      await update();

      if (result.type === "success" && result.data?.name) {
        fullName = result.data.name as string;
      }

      if (fullName && !data.subscription) {
        setTimeout(() => {
          handleSubscription();
        }, 2000);
      }

      isLoadingForm = false;
    };
  }
</script>

<section class="flex min-h-full flex-col justify-center space-y-6 px-4 py-12 lg:py-6">
  <div class="relative space-y-2 text-center sm:mx-auto sm:w-full sm:max-w-md">
    <a
      href="/"
      class="absolute top-auto bottom-auto left-0 inline-flex translate-y-0.5 items-center justify-center gap-x-1 rounded-lg border border-transparent px-3 py-2 text-sm font-semibold tracking-tighter text-white uppercase hover:bg-white/10 disabled:pointer-events-none disabled:opacity-80"
      ><ArrowLeft class="size-5 flex-shrink-0" /> Back</a
    >
    <img class="mx-auto h-10 w-auto select-none" src="/logo.svg" alt="Macro Edge" />
  </div>

  <div
    class="space-y-8 rounded-lg bg-white/3 px-4 py-6 sm:mx-auto sm:w-full sm:max-w-md sm:rounded-2xl"
  >
    {#if !fullName && !data.subscription}
      <div class="text-center">
        <h2 class="text-2xl leading-none font-semibold tracking-tighter">Complete Your Profile</h2>
        <p class="mt-2 text-sm text-gray-300">Before continuing, please update your profile.</p>
      </div>
      <form
        method="POST"
        action="?/updateName"
        use:enhance={handleSubmit}
        class="flex flex-col gap-y-2"
      >
        <div class="flex flex-col gap-y-2">
          <label for="firstname" class="block text-sm leading-6 font-semibold text-white"
            >Full Name</label
          >
          <input
            type="text"
            name="name"
            id="name"
            autocomplete="on"
            value={form?.name}
            placeholder="Full Name"
            class="block w-full rounded-md border-0 bg-white/5 px-3.5 py-2 text-white shadow-sm ring-1 ring-transparent ring-inset focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white sm:text-sm sm:leading-6"
            required
          />
        </div>
        <button
          type="submit"
          class="text-md inline-flex w-full cursor-pointer items-center justify-center gap-x-2 rounded-lg border border-transparent bg-white/6 px-4 py-2 font-medium tracking-tighter text-white shadow-xs hover:bg-white/10 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-90"
          disabled={isLoadingForm}
        >
          {#if isLoadingForm}
            <svg class="mr-2 size-5 animate-spin text-white" viewBox="0 0 24 24" fill="none">
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              />
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
              />
            </svg>
            <span>Updating…</span>
          {:else}
            <Pencil class="size-5 select-none" />
            <span>Update</span>
          {/if}
        </button>

        {#if form?.error}
          <p class="text-center text-sm text-red-500">{form.error}</p>
        {/if}
      </form>
    {:else if fullName && !data.subscription}
      <div class="flex items-center justify-center px-5">
        <div class="shrink-0">
          <img
            class="size-15 rounded-full select-none"
            src={data.session.user?.image || "/user.png"}
            alt={fullName}
          />
        </div>

        <div class="ml-3 text-left">
          <div class="text-lg font-medium tracking-tighter">{fullName}</div>
          <div class="text-sm text-gray-400">{data.session.user?.email}</div>
        </div>
      </div>

      <ul role="list" class="space-y-2 text-center text-lg text-gray-300">
        <li class="flex items-center justify-center gap-x-2">
          <Check class="size-6 flex-none text-emerald-500" /> Your profile is ready
        </li>
        <li class="flex items-center justify-center gap-x-2">
          <svg class="size-5 animate-spin text-white" viewBox="0 0 24 24" fill="none">
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            />
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
            />
          </svg> Starting your free trial…
        </li>
      </ul>
    {/if}
  </div>
  <p class="text-center text-sm text-gray-400">© {currentYear} Macro Edge, All rights reserved.</p>
</section>

<div
  class="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl"
  aria-hidden="true"
>
  <div
    class="mx-auto aspect-1155/678 w-[72.1875rem] bg-linear-to-tr from-emerald-500 to-blue-600 opacity-30"
    style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
  ></div>
</div>

<style>
  :global(html),
  :global(body) {
    height: 100%;
  }
</style>
