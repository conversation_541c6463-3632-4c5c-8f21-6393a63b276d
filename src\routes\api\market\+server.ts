import { json } from "@sveltejs/kit";
// import { getDynamicTTL } from "$lib/utils";
// import { services } from "$lib/server/services";
import type { RequestHandler } from "@sveltejs/kit";

export const GET: RequestHandler = async ({ url }) => {
  const id = url.searchParams.get("id");
  const period = url.searchParams.get("period");
  const interval = url.searchParams.get("interval");

  if (!id || !period || !interval) {
    return json({ error: "Missing id, period, or interval parameter" }, { status: 400 });
  }

  // const redis = services.redis();
  // const cacheKey = `market:${id}:${period}:${interval}`;
  // const cached = await redis.get(cacheKey);

  // if (cached) {
  //   return json(cached, { status: 200 });
  // }

  try {
    const response = await fetch(
      `https://investing-fly.fly.dev/${id}?period=${period}&interval=${interval}`
    );

    const data = await response.json();

    if (!response.ok || !data || data.error) {
      return json({ error: "Error fetching data" }, { status: 400 });
    }

    let prev = data.data[data.data.length - 2][4];
    if (id === "1910" && prev > 0 && prev < 0.1) prev = prev * 100;
    const last = data.data[data.data.length - 1][4];
    const day = ((last - prev) / prev) * 100;
    const results = { price: last, day: day };

    // const ttl = getDynamicTTL(interval);
    // await redis.setex(cacheKey, ttl, JSON.stringify(results));

    return json(results, { status: 200 });
  } catch (error) {
    return json({ error: error }, { status: 500 });
  }
};
