<script lang="ts">
  let props = $props();
  const changeClass = (value: number) =>
    value > 0.002 ? "text-green-500" : value < -0.002 ? "text-red-500" : "";
  const formatChange = (value: number) => `${value > 0.002 ? "+" : ""}${value.toFixed(2)}%`;
</script>

<div class="w-full overflow-hidden">
  <table class="min-w-full tracking-tight">
    <thead>
      <tr>
        <th
          class="w-full rounded-l-md border-white/5 bg-white/5 px-2 py-2.5 text-left text-sm leading-none font-medium uppercase"
          >{props.title}</th
        >
        <th
          class="w-full border-white/5 bg-white/5 py-2.5 text-center text-xs leading-none font-medium uppercase"
        ></th>
        <th
          class="w-full rounded-r-md border-white/5 bg-white/5 py-2 text-center text-sm leading-none font-medium uppercase {changeClass(
            props.average
          )}"
        >
          {#if props.average == null}
            <svg
              class="mx-auto size-4 animate-spin text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              ><circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle><path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path></svg
            >
          {:else}
            {formatChange(props.average)}
          {/if}
        </th>
      </tr>
    </thead>

    <tbody>
      {#each props.items as item}
        <tr>
          <td class="w-full px-2 py-1.5 text-left text-sm leading-none whitespace-nowrap uppercase">
            <div class="flex items-center gap-1">
              <img
                src="/icons/{item.logo}"
                alt={item.name}
                class="size-4 rounded-xs select-none"
                width="16"
                height="16"
              />
              <p>{item.name}</p>
            </div>
          </td>
          <td class="w-full px-2 py-1.5 text-center text-sm leading-none whitespace-nowrap"
            >{item.price}</td
          >
          {#if item.day == null}
            <td class="w-full px-2 py-1.5 text-center text-sm leading-none whitespace-nowrap"
              ><svg
                class="mx-auto size-4 animate-spin text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                ><circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle><path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path></svg
              ></td
            >
          {:else}
            <td
              class="w-full px-2 py-1.5 text-center text-sm leading-none whitespace-nowrap {changeClass(
                item.day
              )}">{formatChange(item.day)}</td
            >
          {/if}
        </tr>
      {/each}
    </tbody>
  </table>
</div>
