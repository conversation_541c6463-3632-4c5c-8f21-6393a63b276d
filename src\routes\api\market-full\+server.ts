import { json } from "@sveltejs/kit";
// import { getDynamicTTL } from "$lib/utils";
// import { services } from "$lib/server/services";
import type { RequestHandler } from "@sveltejs/kit";

export const GET: RequestHandler = async ({ url }) => {
  const id = url.searchParams.get("id");
  const period = url.searchParams.get("period");
  const interval = url.searchParams.get("interval");
  const max = url.searchParams.get("max");

  if (!id || !period || !interval) {
    return json({ error: "Missing id, period, or interval parameter" }, { status: 400 });
  }

  // const redis = services.redis();
  // const cacheKey = `marketfull:${id}:${period}:${interval}:${max ?? "all"}`;
  // const cached = await redis.get(cacheKey);

  // if (cached) {
  //   return json(cached, { status: 200 });
  // }

  try {
    const response = await fetch(
      `https://investing-fly.fly.dev/${id}?period=${period}&interval=${interval}`
    );

    const data = await response.json();

    if (!response.ok || !data || data.error) {
      return json({ error: "Error fetching data" }, { status: 400 });
    }

    const maxNumber = max ? Number(max) : undefined;
    const slicedData = maxNumber ? data.data.slice(-maxNumber) : data.data;
    const results = { data: slicedData };

    // const ttl = getDynamicTTL(interval);
    // await redis.setex(cacheKey, ttl, JSON.stringify(results));

    return json(results, { status: 200 });
  } catch (error) {
    return json({ error: error }, { status: 500 });
  }
};
