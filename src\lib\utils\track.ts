/* eslint-disable prefer-rest-params */
/* eslint-disable prefer-spread */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { browser } from "$app/environment";
import posthog from "posthog-js";

export function initTrack() {
  if (!browser) return;

  const analytics = localStorage.getItem("cookie-analytics");
  const marketing = localStorage.getItem("cookie-marketing");

  if (analytics === "true") {
    posthog.init("phc_2uVsWQVwVkyGVAuSUzfxUUbsR38U64RH7xjzlkmxciI", {
      api_host: "https://eu.i.posthog.com",
      person_profiles: "identified_only",
      capture_pageview: "history_change"
    });

    posthog.opt_in_capturing();
  } else if (analytics === "false") {
    posthog.opt_out_capturing();

    Object.keys(localStorage).forEach((key) => {
      if (
        key.startsWith("__ph_") ||
        key.startsWith("ph_") ||
        key.startsWith("posthog") ||
        key.includes("phc_")
      ) {
        localStorage.removeItem(key);
      }
    });

    document.cookie
      .split(";")
      .map((c) => c.trim())
      .forEach((cookie) => {
        if (cookie.startsWith("ph_") || cookie.includes("posthog")) {
          const name = cookie.split("=")[0];
          document.cookie = `${name}=; Max-Age=0; path=/; SameSite=Lax`;
        }
      });

    Object.keys(sessionStorage).forEach((key) => {
      if (key.startsWith("ph_") || key.startsWith("posthog") || key.includes("phc_")) {
        sessionStorage.removeItem(key);
      }
    });
  }

  if (marketing === "true") {
    (window as any).dataLayer = (window as any).dataLayer || [];
    (window as any).gtag =
      (window as any).gtag ||
      function () {
        (window as any).dataLayer.push(arguments);
      };

    if (!(window as any).gtagInitialized) {
      const gtagScript = document.createElement("script");
      gtagScript.src = "https://www.googletagmanager.com/gtag/js?id=AW-17106500203";
      gtagScript.async = true;

      gtagScript.onload = () => {
        (window as any).gtag("js", new Date());
        (window as any).gtag("config", "AW-17106500203");
        (window as any).gtagInitialized = true;
      };

      document.head.appendChild(gtagScript);
    }

    if (!(window as any).fbqInitialized) {
      (function (f: any, b: any, e: any, v: any, n?: any, t?: any, s?: any) {
        if (f.fbq) return;
        n = f.fbq = function () {
          n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = "2.0";
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s);
      })(window, document, "script", "https://connect.facebook.net/en_US/fbevents.js");

      (window as any).fbq("init", "687516994034347");
      (window as any).fbq("track", "PageView");
      (window as any).fbqInitialized = true;
    }
  }
}
