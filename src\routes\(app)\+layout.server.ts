import { useSession } from "$lib/server/hooks/useSession";
import { redirect } from "@sveltejs/kit";

export async function load({ request, url }) {
  const { session, subscription } = await useSession(request);

  if (!session) throw redirect(302, "/login");

  if (!session.user.name || !subscription) {
    throw redirect(302, "/setup");
  }

  if (
    url.pathname !== "/account" &&
    subscription.status !== "active" &&
    subscription.status !== "trialing"
  ) {
    throw redirect(302, "/account");
  }

  return { session, subscription };
}
