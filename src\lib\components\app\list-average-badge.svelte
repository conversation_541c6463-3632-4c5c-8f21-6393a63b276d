<script lang="ts">
  let props = $props();
  const changeClass = (value: number) =>
    value > 0.002 ? "text-green-500" : value < -0.002 ? "text-red-500" : "";
  const formatChange = (value: number) => `${value > 0.002 ? "+" : ""}${value.toFixed(2)}%`;
  const statusClass = (value: number) =>
    value < -0.002
      ? "bg-green-500/10 text-green-500 ring-green-500/20"
      : value > 0.002
        ? "bg-red-500/10 text-red-500 ring-red-500/20"
        : "bg-white/10 text-white ring-white/20";
  const statusText = (value: number) => (value < -0.002 ? "On" : value > 0.002 ? "Off" : "N/A");
</script>

<div class="w-full tracking-tighter">
  <div class="flex w-full justify-between gap-2 rounded-md border-white/5 bg-white/5 px-2 py-2.5">
    <h2 class="relative text-sm leading-none font-medium uppercase">
      {props.title}

      {#if props.average != null}
        <span
          class="absolute -top-[5px] -right-2 w-10 translate-x-full rounded-md py-1.5 text-center text-xs leading-none font-medium uppercase ring-1 ring-inset {statusClass(
            props.average
          )}">{statusText(props.average)}</span
        >
      {/if}
    </h2>

    {#if props.average == null}
      <svg
        class="size-4 animate-spin text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        ><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"
        ></circle><path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path></svg
      >
    {:else}
      <p class="flex items-center text-sm {changeClass(props.average)}">
        <span class="leading-none">{formatChange(props.average)}</span>
      </p>
    {/if}
  </div>

  <ul>
    {#each props.items as item}
      <li class="flex items-center justify-between px-2 py-1.5">
        <div class="flex items-center gap-1">
          <img
            src="/icons/{item.logo}"
            alt={item.name}
            class="size-4 rounded-xs select-none"
            width="16"
            height="16"
          />
          <h3 class="text-sm leading-none uppercase">{item.name}</h3>
        </div>
        {#if item.day == null}
          <svg
            class="size-4 animate-spin text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            ><circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle><path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path></svg
          >
        {:else}
          <div class="flex items-center text-sm {changeClass(item.day)}">
            <p class="leading-none">{formatChange(item.day)}</p>
          </div>
        {/if}
      </li>
    {/each}
  </ul>
</div>
