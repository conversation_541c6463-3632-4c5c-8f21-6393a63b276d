import { auth } from "$lib/auth";
import { json } from "@sveltejs/kit";
import { services } from "$lib/server/services";

export async function POST({ request, url }) {
  const stripeClient = services.stripe();

  const session = await auth().api.getSession({
    headers: request.headers
  });

  const user = session?.user;

  if (!user?.stripeCustomerId) {
    return json({ error: "Missing Stripe customer ID" }, { status: 400 });
  }
  const portalSession = await stripeClient.billingPortal.sessions.create({
    customer: user.stripeCustomerId,
    return_url: `${url.origin}/account`
  });

  return json({ url: portalSession.url });
}
