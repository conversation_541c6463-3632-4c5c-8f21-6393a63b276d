<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  let widgetContainer: HTMLElement | null = null;

  onMount(() => {
    if (widgetContainer) {
      widgetContainer.innerHTML = "";

      const iframe = document.createElement("iframe");
      iframe.style.width = "100%";
      iframe.style.height = "100%";
      iframe.setAttribute("scrolling", "no");
      iframe.setAttribute("frameborder", "0");
      iframe.setAttribute("allowtransparency", "true");
      iframe.src = `https://www.tradingview-widget.com/embed-widget/events/?locale=en#${encodeURIComponent(`{"width":"100%","height":"100%","colorTheme":"dark","isTransparent":true,"importanceFilter":"0,1","countryFilter":"us,ca,au,jp,ch,eu,gb","utm_source":"www.tradingview.com","utm_medium":"widget_new","utm_campaign":"events","page-uri":"www.tradingview.com/widget-wizard/en/light/economic-calendar/"}`)}`;

      widgetContainer.appendChild(iframe);
    }
  });

  onDestroy(() => {
    if (widgetContainer) {
      while (widgetContainer.firstChild) {
        widgetContainer.removeChild(widgetContainer.firstChild);
      }
    }
  });
</script>

<div bind:this={widgetContainer} class="h-full w-full"></div>
