import { json } from "@sveltejs/kit";
import { getDynamicTTL } from "$lib/utils";
import { services } from "$lib/server/services";
import type { RequestHandler } from "@sveltejs/kit";

type CalendarItem = {
  id: number;
  time: string;
  flag: string;
  currency: string;
  importance: number;
  event: string;
  actual: string;
  forecast: string;
  previous: string;
  signalFX: number | null;
  signalEQ: number | null;
};

type CalendarAPIResponse = {
  data: CalendarItem[];
};

export const GET: RequestHandler = async () => {
  const redis = services.redis();
  const cacheKey = `calendar`;
  const cached = await redis.get(cacheKey);

  let data: CalendarAPIResponse;

  if (cached) {
    data = typeof cached === "string" ? JSON.parse(cached) : cached;
  } else {
    try {
      const response = await fetch(`https://investing-calendar.fly.dev/calendar`);
      const fetched = await response.json();

      if (!response.ok || !fetched || fetched.error) {
        return json({ error: "Error fetching data" }, { status: 400 });
      }

      data = { data: fetched.data };
      const ttl = getDynamicTTL("P1D");
      await redis.setex(cacheKey, ttl, JSON.stringify(data));
    } catch (error) {
      return json({ error: error }, { status: 500 });
    }
  }

  const nowUTC = new Date();

  const itemsToUpdate = data.data.filter((item) => {
    if (
      (item.signalFX !== null && item.signalEQ !== null) ||
      item.time === "All Day" ||
      !item.previous
    )
      return false;

    const [hourStr, minuteStr] = item.time.split(":");
    const hour = parseInt(hourStr, 10);
    const minute = parseInt(minuteStr, 10);

    const itemTimeUTC = new Date(
      Date.UTC(nowUTC.getUTCFullYear(), nowUTC.getUTCMonth(), nowUTC.getUTCDate(), hour, minute, 0)
    );

    return nowUTC >= itemTimeUTC;
  });

  if (itemsToUpdate.length > 0) {
    try {
      const freshResponse = await fetch(`https://investing-calendar.fly.dev/calendar`);
      const freshData = await freshResponse.json();

      if (!freshResponse.ok || !freshData || freshData.error) {
        return json({ error: "Error fetching fresh data" }, { status: 400 });
      }

      const freshItems: CalendarItem[] = freshData.data;

      for (const item of itemsToUpdate) {
        const freshItem = freshItems.find((i) => i.id === item.id);
        if (!freshItem || !freshItem.actual) continue;

        const index = data.data.findIndex((i) => i.id === item.id);
        if (index === -1) continue;

        data.data[index].actual = freshItem.actual;

        const aiInput = { data: [data.data[index]] };
        const aiResponse = await openAI(JSON.stringify(aiInput));
        if (!aiResponse) continue;

        const parsed = JSON.parse(aiResponse) as CalendarAPIResponse;

        if (parsed.data?.[0]?.signalFX !== undefined) {
          data.data[index].signalFX = parsed.data[0].signalFX;
        }

        if (parsed.data?.[0]?.signalEQ !== undefined) {
          data.data[index].signalEQ = parsed.data[0].signalEQ;
        }
      }

      const ttl = getDynamicTTL("P1D");
      await redis.setex(cacheKey, ttl, JSON.stringify(data));

      return json(data, { status: 200 });
    } catch (error) {
      return json({ error: "AI or fetch error", detail: error }, { status: 500 });
    }
  }

  return json(data, { status: 200 });
};

async function openAI(data: string) {
  const openai = services.openai();
  const response = await openai.responses.create({
    model: "gpt-4o-2024-11-20",
    instructions: `
      Please analyse each economic indicator of the country where the signalFX,signalEQ field is null and actual, forecast, and previous values are available and actual is not null.

      - If the data is good for the currency (bullish), set "signalFX": 1.
      - If the data is good for the equities (bullish), set "signalEQ": 1.
      - If it's bad for the currency (bearish), set "signalFX": -1.
      - If it's bad for the equities (bearish), set "signalEQ": -1.
      - If it's neutral for the currency, set "signalFX": 0.
      - If it's neutral for the equities, set "signalEQ": 0.
      - If it's actual empty, set "signalFX": null, "signalEQ": null.

      Return the updated JSON object with the exact same structure.

      🚫 DO NOT use code blocks (no \`\`\`).
      🚫 DO NOT use markdown.
      ✅ ONLY return raw JSON text (no explanation, no formatting).
    `.trim(),
    input: data
  });

  const output = response.output_text;

  if (!output) return data;

  return output
    .replace(/```json\n?/, "")
    .replace(/```$/, "")
    .trim();
}
