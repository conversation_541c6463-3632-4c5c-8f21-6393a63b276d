<script lang="ts">
  import { onMount } from "svelte";

  let props = $props();
  let time = $state(new Date());
  let hours = $derived(time.getHours());
  let minutes = $derived(time.getMinutes());
  let seconds = $derived(time.getSeconds());

  onMount(() => {
    const interval = setInterval(() => {
      time = new Date();
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  });
</script>

<div class="w-full tracking-tighter">
  <div class="flex w-full justify-between gap-2 rounded-md border-white/5 bg-white/5 px-2 py-2.5">
    <h2 class="text-sm leading-none font-medium uppercase">Session</h2>
  </div>
  <div class="mt-2 space-y-2 text-center">
    <div class="grid grid-cols-4 text-center text-sm font-medium">
      <p class="flex flex-col items-center justify-center gap-1 leading-none uppercase">
        <span class="flex items-center gap-1">
          <img src="/icons/JP.svg" alt="Tokyo" class="size-4 rounded-xs select-none" />
          TKY
        </span>
        <span
          class="rounded-md px-2 py-1 text-center text-xs leading-none font-medium uppercase ring-1 ring-inset {props.tokyoStatus ===
          'OPEN'
            ? `bg-green-500/10 text-green-500 ring-green-500/20`
            : `bg-gray-400/10 text-gray-400 ring-gray-400/20`}">{props.tokyoStatus}</span
        >
      </p>
      <p class="flex flex-col items-center justify-center gap-1 leading-none uppercase">
        <span class="flex items-center gap-1">
          <img src="/icons/GB.svg" alt="London" class="size-4 rounded-xs select-none" />
          LDN
        </span>
        <span
          class="rounded-md px-2 py-1 text-center text-xs leading-none font-medium uppercase ring-1 ring-inset {props.londonStatus ===
          'OPEN'
            ? `bg-green-500/10 text-green-500 ring-green-500/20`
            : `bg-gray-400/10 text-gray-400 ring-gray-400/20`}">{props.londonStatus}</span
        >
      </p>
      <p class="flex flex-col items-center justify-center gap-1 leading-none uppercase">
        <span class="flex items-center gap-1">
          <img src="/icons/US.svg" alt="New York" class="size-4 rounded-xs select-none" />
          NYC
        </span>
        <span
          class="rounded-md px-2 py-1 text-center text-xs leading-none font-medium uppercase ring-1 ring-inset {props.newyorkStatus ===
          'OPEN'
            ? `bg-green-500/10 text-green-500 ring-green-500/20`
            : `bg-gray-400/10 text-gray-400 ring-gray-400/20`}">{props.newyorkStatus}</span
        >
      </p>
      <p class="flex flex-col items-center justify-center gap-1 leading-none uppercase">
        <span class="flex items-center gap-1">
          <img src="/icons/US.svg" alt="NYSE" class="size-4 rounded-xs select-none" />
          NYSE
        </span>
        <span
          class="rounded-md px-2 py-1 text-center text-xs leading-none font-medium uppercase ring-1 ring-inset {props.nyseStatus ===
          'OPEN'
            ? `bg-green-500/10 text-green-500 ring-green-500/20`
            : `bg-gray-400/10 text-gray-400 ring-gray-400/20`}">{props.nyseStatus}</span
        >
      </p>
    </div>
  </div>

  <div
    class="mt-1 px-2 py-1 text-center text-5xl leading-none font-light -tracking-tighter uppercase"
  >
    {String(hours).padStart(2, "0")}:{String(minutes).padStart(2, "0")}:{String(seconds).padStart(
      2,
      "0"
    )}
  </div>
</div>
