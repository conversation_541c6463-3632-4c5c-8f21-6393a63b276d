function normalize(value: number, weight: number, correlation: number) {
  if (
    value == null ||
    isNaN(value) ||
    correlation == null ||
    isNaN(correlation) ||
    weight == null ||
    isNaN(weight)
  )
    return 0;

  const normalized = ((Math.abs(value) + 50) / 100) * 10 * Math.sign(value);
  return normalized * (weight / 100.0) * correlation;
}

export default function calcRisk(
  name: string,
  DXY: number,
  VIX: number,
  US10Y: number,
  US02Y: number,
  EU10Y: number,
  GB10Y: number,
  AU10Y: number,
  CA10Y: number,
  JP10Y: number,
  CH10Y: number,
  TOP10NQ: number,
  TOP10DJ: number
) {
  let DXYScore = 0,
    VIXScore = 0,
    US10YScore = 0,
    US02YScore = 0,
    EU10YScore = 0,
    GB10YScore = 0,
    AU10YScore = 0,
    CA10YScore = 0,
    JP10YScore = 0,
    CH10YScore = 0,
    TOP10NQScore = 0,
    TOP10DJScore = 0;

  if (name === "SP 500" || name === "Nasdaq" || name === "<PERSON> Jones") {
    DXYScore = normalize(DXY, 20, -0.7);
    VIXScore = normalize(VIX, 20, -0.8);
    US02YScore = normalize(US02Y, 15, -0.6);
    US10YScore = normalize(US10Y, 5, -0.7);

    if (name === "SP 500") {
      TOP10NQScore = normalize(TOP10NQ, 25, 0.95);
      TOP10DJScore = normalize(TOP10DJ, 5, 0.95);
    } else if (name === "Nasdaq") {
      TOP10NQScore = normalize(TOP10NQ, 30, 0.95);
    } else if (name === "Dow Jones") {
      TOP10DJScore = normalize(TOP10DJ, 30, 0.95);
    }
  } else if (name === "EUR/USD" || name === "GBP/USD" || name === "AUD/USD" || name === "BTC/USD") {
    DXYScore = normalize(DXY, 40, -0.7);
    US02YScore = normalize(US02Y, 5, -0.6);
    US10YScore = normalize(US10Y, 10, -0.7);

    if (name === "EUR/USD") {
      DXYScore = normalize(DXY, 50, -1);
      EU10YScore = normalize(EU10Y, 10, 0.6);
    } else if (name === "GBP/USD") {
      GB10YScore = normalize(GB10Y, 15, 0.8);
    } else if (name === "AUD/USD") {
      AU10YScore = normalize(AU10Y, 15, 0.8);
    }
  } else if (name === "XAU/USD") {
    DXYScore = normalize(DXY, 35, -0.85);
    VIXScore = normalize(VIX, 10, 0.6);
    US02YScore = normalize(US02Y, 10, -0.75);
    US10YScore = normalize(US10Y, 20, -0.8);
  } else if (name === "USD/CAD" || name === "USD/JPY" || name === "USD/CHF") {
    DXYScore = normalize(DXY, 40, 0.7);
    US02YScore = normalize(US02Y, 5, 0.6);
    US10YScore = normalize(US10Y, 10, 0.7);

    if (name === "USD/CAD") {
      CA10YScore = normalize(CA10Y, 15, -0.8);
    } else if (name === "USD/JPY") {
      JP10YScore = normalize(JP10Y, 15, -0.8);
    } else if (name === "USD/CHF") {
      CH10YScore = normalize(CH10Y, 15, -0.8);
    }
  }

  const TotalScore =
    DXYScore +
    VIXScore +
    US10YScore +
    US02YScore +
    EU10YScore +
    GB10YScore +
    AU10YScore +
    CA10YScore +
    JP10YScore +
    CH10YScore +
    TOP10NQScore +
    TOP10DJScore;

  return TotalScore;
}
