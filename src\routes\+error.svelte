<script lang="ts">
  import { page } from "$app/state";
  import { ArrowLeft } from "lucide-svelte";
</script>

<div class="max-w-3xl min-h-96 h-screen flex flex-col justify-center sm:items-center mx-auto relative">
  <div class="text-center px-4 sm:px-6 lg:px-10">
    <a href="/" class="inline-block text-center select-none -translate-y-8">
      <img src="/logo-icon-white.svg" alt="Macro Edge" class="w-20 sm:w-28" />
    </a>
    <h1 class="block text-7xl font-bold text-white sm:text-9xl tracking-tighter">{page.status}</h1>
    <p class="mt-3 text-white tracking-tighter">Oops, {page.error?.message}</p>
    <div class="mt-5 flex flex-col justify-center items-center gap-2 sm:flex-row sm:gap-3">
      <a href="/" class="w-full sm:w-auto py-3 px-4 inline-flex justify-center items-center gap-x-1 text-sm font-semibold rounded-lg border border-transparent bg-white/5 text-white hover:bg-gray-200/20 disabled:opacity-50 disabled:pointer-events-none tracking-tighter"><ArrowLeft class="flex-shrink-0 size-5" />Back to Home</a>
    </div>
  </div>
</div>

<div class="absolute inset-x-0 top-4 -z-10 flex transform-gpu justify-center overflow-hidden blur-3xl" aria-hidden="true"><div class="aspect-[1108/632] w-[69.25rem] flex-none bg-gradient-to-r from-emerald-500 to-blue-600 opacity-25" style="clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%)"></div></div>
