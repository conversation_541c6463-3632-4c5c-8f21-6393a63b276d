<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import Chart, { type ChartItem, type ChartConfiguration } from "chart.js/auto";

  let props = $props();
  let items = props.items;
  let chartElement: ChartItem;
  let chartInstance: Chart | null = null;

  const updateChart = () => {
    items = props.items;

    if (chartInstance) {
      chartInstance.data.labels = items.map((item: any) => item.time);
      chartInstance.data.datasets[0].data = items.map((item: any) => item.US01Y);
      chartInstance.data.datasets[1].data = items.map((item: any) => item.US02Y);
      chartInstance.data.datasets[2].data = items.map((item: any) => item.US05Y);
      chartInstance.update();
    }
  };

  const chartData = {
    labels: items.map((item: any) => item.time),
    datasets: [
      {
        label: "01Y",
        data: items.map((item: any) => item.US01Y),
        borderColor: "#2b7fff",
        backgroundColor: "#2b7fff",
        borderWidth: 2,
        pointRadius: 0,
        fill: false
      },
      {
        label: "02Y",
        data: items.map((item: any) => item.US02Y),
        borderColor: "#ff6900",
        backgroundColor: "#ff6900",
        borderWidth: 2,
        pointRadius: 0,
        fill: false
      },
      {
        label: "05Y",
        data: items.map((item: any) => item.US05Y),
        borderColor: "#fb2c36",
        backgroundColor: "#fb2c36",
        borderWidth: 2,
        pointRadius: 0,
        fill: false
      }
    ]
  };

  const config: ChartConfiguration = {
    type: "line",
    data: chartData,
    options: {
      responsive: true,
      interaction: { mode: "index", intersect: false },
      animation: { duration: 1000 },
      plugins: {
        legend: {
          display: true,
          labels: {
            color: "hsl(0 0% 100%)",
            boxWidth: 12,
            boxHeight: 8,
            usePointStyle: true,
            pointStyle: "rectRounded"
          },
          position: "bottom"
        },
        tooltip: {
          enabled: true,
          mode: "index",
          intersect: false,
          usePointStyle: true,
          backgroundColor: "rgba(17, 24, 39, 0.95)",
          titleColor: "hsl(0 0% 100%)",
          bodyColor: "hsl(0 0% 100%)",
          displayColors: true,
          borderColor: "rgba(17, 24, 39, 0.95)",
          borderWidth: 1,
          caretSize: 0,
          cornerRadius: 5,
          callbacks: {
            labelPointStyle: () => {
              return { pointStyle: "rectRounded", rotation: 0 };
            }
          }
        }
      },
      elements: {
        point: {
          radius: 0,
          borderWidth: 2,
          hoverRadius: 5,
          hoverBorderWidth: 2,
          pointStyle: "circle"
        }
      },
      scales: {
        x: {
          display: true,
          title: { display: false },
          grid: { color: "rgba(229, 231, 235, 0.05)" },
          ticks: { color: "hsl(0 0% 100%)" }
        },
        y: {
          display: true,
          title: { display: false },
          grid: { color: "rgba(229, 231, 235, 0.05)" },
          ticks: { color: "hsl(0 0% 100%)" }
        }
      }
    }
  };

  $effect(() => {
    updateChart();
  });

  onMount(() => {
    chartInstance = new Chart(chartElement, config);
  });

  onDestroy(() => {
    if (chartInstance) chartInstance.destroy();
  });
</script>

<div class="w-full space-y-2">
  <div class="w-full tracking-tighter">
    <div class="flex w-full justify-between gap-2 rounded-md border-white/5 bg-white/5 px-2 py-2.5">
      <h2 class="text-sm leading-none font-medium uppercase">{props.title}</h2>
    </div>
  </div>

  <div class="w-full">
    <canvas class="w-full select-none" height="215" bind:this={chartElement}></canvas>
  </div>
</div>
