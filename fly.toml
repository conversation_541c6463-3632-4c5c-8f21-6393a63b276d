# fly.toml app configuration file generated for macro-edge-node on 2025-04-30T07:09:16Z
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "macro-edge-node"
primary_region = "ams"

[build]

[http_service]
  force_https = true
  internal_port = 3000
  keepalive_timeout = 120
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

[[vm]]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 1024
