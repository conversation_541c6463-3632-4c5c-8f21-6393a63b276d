<script lang="ts">
  import { onMount } from "svelte";
  import "@fontsource-variable/inter";
  import { initTrack } from "$lib/utils/track";
  import interWoff2 from "@fontsource-variable/inter/files/inter-latin-wght-normal.woff2?url";
  import "../app.css";

  let { children } = $props();

  onMount(() => {
    initTrack();

    const recaptchaScript = document.createElement("script");
    recaptchaScript.src =
      "https://www.google.com/recaptcha/enterprise.js?render=6Lf3UiQrAAAAAOI328aI1jf_-Ak6XN8druAVbqYH";
    recaptchaScript.async = true;
    document.head.appendChild(recaptchaScript);
  });
</script>

<svelte:head>
  <link rel="preload" as="font" type="font/woff2" href={interWoff2} crossorigin="anonymous" />
</svelte:head>

{@render children()}
