<script lang="ts">
  import Header from "./components/header.svelte";
  import Footer from "./components/footer.svelte";

  let { user, url } = $props();
</script>

<!DOCTYPE html>
<html lang="en">
  <body style="margin:0;padding:0;">
    <div
      style="
  background-color: #0f172a;
  font-family: 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  max-width: 600px;
  margin: 0 auto;
  padding: 32px;
  color: #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0,0,0,0.3);
"
    >
      <Header />
      <h1 style="color: #f1f5f9; font-size: 22px; text-align: center; margin-bottom: 16px">Welcome, {user.name} 👋</h1>
      <p style="font-size: 16px; text-align: center; margin-bottom: 32px; color: #f1f5f9">
        We're excited to have you on board! Please confirm your email address to activate your Macro Edge account.
      </p>
      <div style="text-align: center; margin: 24px 0;">
        <a
          href={url}
          style="
            background-color: #10b981;
            color: #fff;
            padding: 14px 28px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            display: inline-block;
          "
        >
          ✉️ Verify Your Email
        </a>
      </div>
      <!-- <p style="font-size: 14px; color: #facc15; text-align: center; margin-bottom: 24px;">
        ⚠️ This link expires in 5 minutes for security.
      </p> -->
      <p style="font-size: 12px; text-align: center; color: #94a3b8;">
        <span style="color: #94a3b8;">Trouble clicking? Copy and paste this link in your browser:</span><br />
        <a href={url} style="color: #10b981;">{url}</a>
      </p>
      <p style="font-size: 14px; color: #94a3b8; text-align: center;">
        Didn't sign up? Just ignore this email and nothing will happen.
      </p>
      <Footer />
    </div>
  </body>
</html>
