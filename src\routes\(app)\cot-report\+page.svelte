<script lang="ts">
  import { page } from "$app/state";
  import { currencies, indices, bonds, metals, agricultural, energy } from "$lib/constants/cot-report";
  import ListLink from "$lib/components/app/list-link.svelte";
</script>

<section class="mx-auto max-w-7xl space-y-5 px-2 py-5">
  <h1 class="text-center text-2xl leading-none font-bold tracking-tighter uppercase">COT Report</h1>

  <div class="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3">
    <ListLink items={currencies} title="Currencies" path={page.url.pathname} />

    <div class="space-y-2">
      <ListLink items={indices} title="Indices" path={page.url.pathname} />
      <ListLink items={bonds} title="Bonds" path={page.url.pathname} />
    </div>

    <div class="col-span-full lg:col-span-1">
      <div class="grid grid-cols-1 gap-2 space-y-2 md:grid-cols-2 lg:grid-cols-2">
        <ListLink items={metals} title="Metals" path={page.url.pathname} />
        <ListLink items={agricultural} title="Agricultural" path={page.url.pathname} />
      </div>

      <ListLink items={energy} title="Energy" path={page.url.pathname} />
    </div>
  </div>
</section>
