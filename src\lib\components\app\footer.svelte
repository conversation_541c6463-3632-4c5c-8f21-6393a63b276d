<script lang="ts">
  import { HeaderLinksApp, LegalLinks, SocialLinks, UserLinksApp } from "$lib/constants/links";
  import Cookies from "$lib/components/cookies.svelte";
  import { authClient } from "$lib/auth-client";
  import { goto } from "$app/navigation";

  const currentYear = new Date().getFullYear();
  export let showCookies: boolean;

  async function handleLogout() {
    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          goto("/");
        }
      }
    });
  }
</script>

<footer class="relative w-full overflow-hidden border-t border-white/8 shadow-lg">
  <div class="relative mx-auto w-full max-w-7xl px-4 pt-8 pb-6 lg:px-8">
    <div class="grid grid-cols-1 gap-4 md:grid-cols-4 md:gap-10">
      <div>
        <div class="flex flex-col gap-y-4 text-sm text-gray-300">
          <p>
            <a class="inline-flex" href={HeaderLinksApp[0].route}
              ><img src="/logo.svg" class="w-48 select-none" alt="Macro Edge" /></a
            >
          </p>
          <div class="flex gap-x-2.5">
            {#each SocialLinks as item}
              {@const Icon = item.icon}
              <a
                href={item.route}
                target="_blank"
                class="inline-flex size-10 items-center justify-center gap-x-2 rounded-lg border border-transparent bg-white/10 text-sm font-semibold hover:bg-emerald-500 hover:text-white"
                ><Icon class="size-5 flex-shrink-0" /></a
              >
            {/each}
          </div>

          <p>
            <strong class="uppercase">Macro Edge LTD</strong><br /> Registration Number 16375195<br
            /> Poole, United Kingdom
          </p>
        </div>
      </div>

      <div>
        <h4 class="text-sm font-medium uppercase">Page Links</h4>
        <div class="mt-2 grid space-y-2 text-sm">
          {#each HeaderLinksApp as item}
            <p>
              <a class="inline-flex gap-x-2 text-gray-300 hover:text-emerald-500" href={item.route}
                >{item.label}</a
              >
            </p>
          {/each}
        </div>
      </div>

      <div>
        <h4 class="text-sm font-medium uppercase">User Links</h4>
        <div class="mt-2 grid space-y-2 text-sm">
          {#each UserLinksApp as item}
            {#if item.label === `Logout`}
              <p>
                <button
                  type="button"
                  class="inline-flex cursor-pointer gap-x-2 text-gray-300 hover:text-emerald-500"
                  onclick={() => handleLogout()}>{item.label}</button
                >
              </p>
            {:else}
              <p>
                <a
                  class="inline-flex gap-x-2 text-gray-300 hover:text-emerald-500"
                  href={item.route}>{item.label}</a
                >
              </p>
            {/if}
          {/each}
        </div>
      </div>

      <div>
        <h4 class="text-sm font-medium uppercase">Legal</h4>
        <div class="mt-2 grid space-y-2 text-sm">
          {#each LegalLinks as item}
            <p>
              <a class="inline-flex gap-x-2 text-gray-300 hover:text-emerald-500" href={item.route}
                >{item.label}</a
              >
            </p>
          {/each}

          <p>
            <button
              class="inline-flex cursor-pointer gap-x-2 text-gray-300 hover:text-emerald-500"
              onclick={() => (showCookies = !showCookies)}>Cookies Settings</button
            >
          </p>
        </div>
      </div>
    </div>
    <div class="mt-5 border-t border-white/8 pt-5 sm:mt-8">
      <div class="sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-300">© {currentYear} Macro Edge, All rights reserved.</p>
        </div>
      </div>
    </div>
  </div>
</footer>

<Cookies bind:showCookies />
