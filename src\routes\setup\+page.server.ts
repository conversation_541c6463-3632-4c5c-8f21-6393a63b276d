import { eq } from "drizzle-orm";
import type { Actions } from "./$types";
import { redirect, fail } from "@sveltejs/kit";
import * as schema from "$lib/server/db/schema";
import { services } from "$lib/server/services";
import { useSession } from "$lib/server/hooks/useSession";

export async function load({ request }) {
  const { session, subscription } = await useSession(request);

  if (!session) throw redirect(302, "/login");

  if (session.user.name && subscription) {
    throw redirect(302, "/market-risk");
  }

  return { session, subscription };
}

export const actions: Actions = {
  updateName: async ({ request }) => {
    const db = services.db();
    const { session } = await useSession(request);

    if (!session) throw redirect(302, "/login");

    if (!session.user.name) {
      const formData = await request.formData();
      const name = formData.get("name")?.toString();

      if (!name) {
        return fail(400, { error: "Name is required." });
      }

      await db.update(schema.user).set({ name }).where(eq(schema.user.id, session.user.id));

      return { success: true, name };
    }
  }
};
