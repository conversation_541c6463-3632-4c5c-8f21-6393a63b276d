import { json } from "@sveltejs/kit";
import type { <PERSON>questHand<PERSON> } from "@sveltejs/kit";

export const GET: RequestHandler = async ({ url }) => {
  const paramsId = url.searchParams.get("id");

  if (!paramsId) {
    return json({ error: "Missing id parameter" }, { status: 400 });
  }

  try {
    const query = new URLSearchParams({
      $limit: "25",
      $offset: "0",
      $order: "report_date_as_yyyy_mm_dd desc",
      $$app_token: "UMfq3Fy78WORHlC75rBdDEa8x",
      $where: `cftc_contract_market_code='${paramsId}'`,
      $select:
        "cftc_contract_market_code,report_date_as_yyyy_mm_dd,noncomm_positions_long_all,noncomm_positions_short_all,comm_positions_long_all,comm_positions_short_all,change_in_noncomm_long_all,change_in_noncomm_short_all,change_in_comm_long_all,change_in_comm_short_all"
    }).toString();

    const response = await fetch(`https://publicreporting.cftc.gov/resource/6dca-aqww.json?${query}`);
    const data = await response.json();

    if (!response.ok || !data) {
      return json({ error: "Failed to fetch COT data" }, { status: 400 });
    }

    const chartData = data.map((item: { [key: string]: string }) => ({
      date: item.report_date_as_yyyy_mm_dd,
      netCommercial: parseInt(item.comm_positions_long_all) - parseInt(item.comm_positions_short_all),
      netNonCommercial: parseInt(item.noncomm_positions_long_all) - parseInt(item.noncomm_positions_short_all),
      longCommercial: parseInt(item.comm_positions_long_all),
      longNonCommercial: parseInt(item.noncomm_positions_long_all),
      shortCommercial: parseInt(item.comm_positions_short_all),
      shortNonCommercial: parseInt(item.noncomm_positions_short_all)
    }));

    const minComm = Math.min(...chartData.map((d: { [key: string]: number }) => d.netCommercial));
    const maxComm = Math.max(...chartData.map((d: { [key: string]: number }) => d.netCommercial));
    const minNonComm = Math.min(...chartData.map((d: { [key: string]: number }) => d.netNonCommercial));
    const maxNonComm = Math.max(...chartData.map((d: { [key: string]: number }) => d.netNonCommercial));

    const indexData = chartData.map((item: { [key: string]: number }) => ({
      date: item.date,
      idxCommercial: maxComm !== minComm ? ((item.netCommercial - minComm) / (maxComm - minComm)) * 100 : null,
      idxNonCommercial:
        maxNonComm !== minNonComm ? ((item.netNonCommercial - minNonComm) / (maxNonComm - minNonComm)) * 100 : null
    }));

    return json({ data, chartData, indexData }, { status: 200 });
  } catch (error) {
    return json({ error: error }, { status: 500 });
  }
};
