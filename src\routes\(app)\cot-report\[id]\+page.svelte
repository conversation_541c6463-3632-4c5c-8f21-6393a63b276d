<script lang="ts">
  import { onMount } from "svelte";
  import { ArrowLeft } from "lucide-svelte";
  import ListTableCOT from "$lib/components/app/list-table-cot.svelte";
  import GraphCot from "$lib/components/app/graph-cot.svelte";

  let { data } = $props();
  let items = $state<any[]>([]);
  let chartData = $state<any[]>([]);
  let indexData = $state<any[]>([]);
  let itemsCommercial = $state<any[]>([]);
  let itemsNonCommercial = $state<any[]>([]);
  let labels = $state<string[]>([]);
  let indexPositions = $state<{ commercial: number[]; nonCommercial: number[] }>({
    commercial: [],
    nonCommercial: []
  });
  let netPositions = $state<{ commercial: number[]; nonCommercial: number[] }>({
    commercial: [],
    nonCommercial: []
  });
  let longPositions = $state<{ commercial: number[]; nonCommercial: number[] }>({
    commercial: [],
    nonCommercial: []
  });
  let shortPositions = $state<{ commercial: number[]; nonCommercial: number[] }>({
    commercial: [],
    nonCommercial: []
  });

  const fetchData = async () => {
    if (!data) return;

    try {
      const response = await fetch(`/api/cot?id=${encodeURIComponent(data.props.id)}`);
      const result = await response.json();

      if (!response.ok) {
        console.error(result.error || "Failed to fetch COT data");
        return;
      }

      items = result.data;
      chartData = result.chartData;
      indexData = result.indexData;

      itemsCommercial = items.map((item: { [key: string]: string }) => ({
        date: item.report_date_as_yyyy_mm_dd,
        net: parseInt(item.comm_positions_long_all) - parseInt(item.comm_positions_short_all),
        long: parseInt(item.comm_positions_long_all),
        short: parseInt(item.comm_positions_short_all),
        changeNet: parseInt(item.change_in_comm_long_all) - parseInt(item.change_in_comm_short_all),
        changeLong: parseInt(item.change_in_comm_long_all),
        changeShort: parseInt(item.change_in_comm_short_all),
        total: parseInt(item.comm_positions_long_all) + parseInt(item.comm_positions_short_all),
        totalChange:
          parseInt(item.change_in_comm_long_all) + parseInt(item.change_in_comm_short_all)
      }));

      itemsNonCommercial = items.map((item: { [key: string]: string }) => ({
        date: item.report_date_as_yyyy_mm_dd,
        net: parseInt(item.noncomm_positions_long_all) - parseInt(item.noncomm_positions_short_all),
        long: parseInt(item.noncomm_positions_long_all),
        short: parseInt(item.noncomm_positions_short_all),
        changeNet:
          parseInt(item.change_in_noncomm_long_all) - parseInt(item.change_in_noncomm_short_all),
        changeLong: parseInt(item.change_in_noncomm_long_all),
        changeShort: parseInt(item.change_in_noncomm_short_all),
        total:
          parseInt(item.noncomm_positions_long_all) + parseInt(item.noncomm_positions_short_all),
        totalChange:
          parseInt(item.change_in_noncomm_long_all) + parseInt(item.change_in_noncomm_short_all)
      }));

      labels = chartData.map((d: { [key: string]: string }) => d.date);

      indexPositions = {
        commercial: indexData.map((d: { [key: string]: number }) => d.idxCommercial),
        nonCommercial: indexData.map((d: { [key: string]: number }) => d.idxNonCommercial)
      };

      netPositions = {
        commercial: chartData.map((d: { [key: string]: number }) => d.netCommercial),
        nonCommercial: chartData.map((d: { [key: string]: number }) => d.netNonCommercial)
      };

      longPositions = {
        commercial: chartData.map((d: { [key: string]: number }) => d.longCommercial),
        nonCommercial: chartData.map((d: { [key: string]: number }) => d.longNonCommercial)
      };

      shortPositions = {
        commercial: chartData.map((d: { [key: string]: number }) => d.shortCommercial),
        nonCommercial: chartData.map((d: { [key: string]: number }) => d.shortNonCommercial)
      };
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  onMount(() => {
    fetchData();
  });
</script>

<section class="mx-auto max-w-7xl space-y-3.5 px-2 py-5">
  <div class="-mt-2 flex items-center gap-2">
    <a
      href="/cot-report"
      class="inline-flex items-center justify-center gap-x-1 rounded-lg border border-transparent px-3 py-2 text-sm font-semibold tracking-tighter text-white uppercase hover:bg-white/10 disabled:pointer-events-none disabled:opacity-50"
      ><ArrowLeft class="size-5 flex-shrink-0" /> Back</a
    >
    <div class="flex items-center gap-2">
      {#if items.length === 0}
        <svg
          class="size-6 animate-spin text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          ><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"
          ></circle><path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path></svg
        >
      {:else}
        <img
          src="/icons/{data.props.logo}"
          alt={data.props.name}
          class="size-6 rounded-xs select-none"
          width="24"
          height="24"
        />
      {/if}

      <h1
        class="overflow-hidden text-center text-2xl leading-none font-bold tracking-tighter text-ellipsis whitespace-nowrap uppercase"
      >
        {data.props.name}
      </h1>
    </div>
  </div>

  <div class="space-y-2">
    <div class="grid grid-cols-1 gap-2 md:grid-cols-2">
      <GraphCot title="COT Index" {labels} items={indexPositions} />
      <GraphCot title="Net Positions" {labels} items={netPositions} />
      <GraphCot title="Long Positions" {labels} items={longPositions} />
      <GraphCot title="Short Positions" {labels} items={shortPositions} />
    </div>

    <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
      <ListTableCOT title="Non Commercial" items={itemsNonCommercial} />
      <ListTableCOT title="Commercial" items={itemsCommercial} />
    </div>
  </div>
</section>
