import { auth } from "$lib/auth";
import { eq, and, not, desc } from "drizzle-orm";
import * as schema from "$lib/server/db/schema";
import { services } from "$lib/server/services";

export async function useSession(request: Request) {
  const db = services.db();

  const session = await auth().api.getSession({
    headers: request.headers
  });

  if (!session) return { session: null, subscription: null };

  const [subscription] = await db
    .select()
    .from(schema.subscription)
    .where(
      and(
        eq(schema.subscription.referenceId, session.user.id),
        not(eq(schema.subscription.status, "incomplete"))
      )
    )
    .orderBy(desc(schema.subscription.periodStart))
    .limit(1);

  return { session, subscription: subscription ?? null };
}
