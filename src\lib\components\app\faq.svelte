<script lang="ts">
  import { Plus, Minus } from "lucide-svelte";
  import { faqs } from "$lib/constants/faqs";

  let importFaqs = [...faqs];

  function toggleFAQ(index: number) {
    importFaqs = importFaqs.map((faq) => ({ ...faq, open: false }));
    importFaqs[index].open = !importFaqs[index].open;
    importFaqs = [...importFaqs];
  }
</script>

<section class="relative">
  <div class="mx-auto max-w-7xl space-y-5 px-4 py-16 lg:px-8">
    <div class="text-center">
      <h2 class="text-4xl leading-none font-bold tracking-tighter">Frequently Asked Questions</h2>
      <p class="mt-2 text-lg text-gray-300">
        Find answers to common questions about Macro Edge, its features, pricing, and how to get
        started.
      </p>
    </div>

    <div class="w-full space-y-2">
      {#each importFaqs as faq, i}
        <div class="rounded-lg shadow-sm hover:bg-white/8 {faq.open ? 'bg-white/5' : 'bg-white/3'}">
          <button
            type="button"
            onclick={() => toggleFAQ(i)}
            class="text-md inline-flex w-full cursor-pointer items-center justify-start gap-x-2 p-4 leading-none font-medium"
          >
            <Plus class="h-6 w-6 text-emerald-600 {!faq.open ? '' : 'hidden'}" />
            <Minus class="h-6 w-6 text-emerald-600 {faq.open ? '' : 'hidden'}" />
            {faq.question}
          </button>
          {#if faq.open}
            <p class="p-4 text-gray-300">{faq.answer}</p>
          {/if}
        </div>
      {/each}
    </div>
  </div>

  <div
    class="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl"
    aria-hidden="true"
  >
    <div
      class="mx-auto aspect-1155/678 w-[72.1875rem] bg-linear-to-tr from-emerald-500 to-blue-600 opacity-30"
      style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
    ></div>
  </div>
</section>
