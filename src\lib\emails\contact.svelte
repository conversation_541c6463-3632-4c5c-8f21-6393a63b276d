<script lang="ts">
  let { data } = $props();
  const currentYear = new Date().getFullYear();
</script>

<!DOCTYPE html>
<html lang="en">
  <body style="margin: 0; padding: 0">
    <div
      style="
        background-color: #fff;
        font-family: 'Se<PERSON>e UI', Roboto, Helvetica, Arial, sans-serif;
        max-width: 600px;
        margin: 0 auto;
        padding: 32px;
        color: #0f172a;
        border-radius: 8px;
      "
    >
      <!-- Header -->
      <div style="text-align: center; margin-bottom: 32px">
        <a href="https://macroedge.pro" target="_blank" style="text-decoration: none">
          <img src="https://macroedge.pro/logo.png" alt="Macro Edge" style="height: 40px" />
        </a>
      </div>

      <!-- Content -->
      <h1 style="color: #0f172a; font-size: 22px; text-align: center; margin-bottom: 16px">
        {data.firstname}
        {data.lastname}
      </h1>
      <p style="font-size: 18px; color: #0f172a; text-align: center">{@html data.message}</p>

      <!-- Footer -->
      <div
        style="
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #1e293b;
          text-align: center;
          font-size: 12px;
          color: #0f172a;
        "
      >
        &copy; {currentYear} Macro Edge. All rights reserved.<br />
        <a href="https://macroedge.pro" style="color: #10b981; text-decoration: none"
          >macroedge.pro</a
        >
      </div>
    </div>
  </body>
</html>
