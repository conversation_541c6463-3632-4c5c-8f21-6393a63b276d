import { error } from "@sveltejs/kit";
import { currencies, indices, bonds, metals, agricultural, energy } from "$lib/constants/cot-report";

export async function load({ params }) {
  if (!params.id) {
    throw error(404, "Not found");
  }

  const allItems = [...currencies, ...indices, ...bonds, ...metals, ...agricultural, ...energy];
  const props = allItems.find((item) => item.id === params.id);

  if (!props) {
    throw error(404, "Item not found in constants");
  }

  return {
    props
  };
}
