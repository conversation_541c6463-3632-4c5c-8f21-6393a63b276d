export function lazy<T>(factory: () => T): () => T {
  let instance: T | undefined;
  return () => {
    if (!instance) {
      instance = factory();
    }
    return instance;
  };
}

export function getDynamicTTL(interval: string): number {
  const now = new Date();
  const seconds = now.getUTCSeconds();
  const minutes = now.getUTCMinutes();

  if (interval === "PT1M") {
    return 60 - seconds;
  }

  if (interval === "PT5M") {
    const next5min = 5 - (minutes % 5);
    return next5min * 60 - seconds;
  }

  if (interval === "PT15M") {
    const next15min = 15 - (minutes % 15);
    return next15min * 60 - seconds;
  }

  if (interval === "PT1H") {
    const minutesLeft = 59 - minutes;
    return minutesLeft * 60 + (60 - seconds);
  }

  if (interval === "P1D") {
    const midnight = new Date(now);
    const day = now.getUTCDay();

    let daysToAdd = 1;

    if (day === 5) daysToAdd = 3;
    else if (day === 6) daysToAdd = 2;
    else if (day === 0) daysToAdd = 1;

    midnight.setUTCDate(midnight.getUTCDate() + daysToAdd);
    midnight.setUTCHours(0, 0, 0, 0);

    return Math.floor((midnight.getTime() - now.getTime()) / 1000);
  }

  if (interval === "FRIDAY") {
    const target = new Date(
      Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 19, 31, 0, 0)
    );

    const day = now.getUTCDay();

    if (day === 5 && now >= target) {
      target.setUTCDate(target.getUTCDate() + 7); // Next Friday
    } else if (day === 6) {
      target.setUTCDate(target.getUTCDate() + 6); // Saturday → next Friday
    } else if (day === 0) {
      target.setUTCDate(target.getUTCDate() + 5); // Sunday → next Friday
    } else {
      target.setUTCDate(target.getUTCDate() + (5 - day)); // Monday–Friday before 19:31
    }

    return Math.floor((target.getTime() - now.getTime()) / 1000);
  }

  return 60;
}
