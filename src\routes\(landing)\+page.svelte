<script lang="ts">
  import Hero from "$lib/components/landing/hero.svelte";
  import WhyUs from "$lib/components/landing/why-us.svelte";
  import Features from "$lib/components/landing/features.svelte";
  import Pricing from "$lib/components/landing/pricing.svelte";
  import Faq from "$lib/components/landing/faq.svelte";
  import Contact from "$lib/components/landing/contact.svelte";
  import { HeaderLinks, LegalLinks, SocialLinks } from "$lib/constants/links";

  let { form } = $props();

  const softwareSchema = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    name: "Macro Edge",
    url: "https://macroedge.pro",
    image: "https://macroedge.pro/og-en.png",
    description:
      "Macro Edge is a SaaS platform for FX traders, providing real-time macroeconomic data, predictive algo signals, institutional COT insights, and market sentiment tools.",
    applicationCategory: "FinanceApplication",
    operatingSystem: "Web",
    offers: {
      "@type": "Offer",
      price: "20.00",
      priceCurrency: "USD",
      availability: "https://schema.org/InStock"
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.9",
      reviewCount: "3042"
    }
  };

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "Macro Edge",
    url: "https://macroedge.pro"
  };

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "Macro Edge",
    url: "https://macroedge.pro",
    logo: "https://macroedge.pro/icon.png",
    email: "<EMAIL>",
    sameAs: [...SocialLinks.map((item) => item.route)],
    description:
      "Macro Edge is a SaaS platform for FX traders, providing real-time macroeconomic data, predictive algo signals, institutional COT insights, and market sentiment tools.",
    address: {
      "@type": "PostalAddress",
      streetAddress: "Remote Office",
      addressLocality: "Global",
      addressCountry: "UK"
    }
  };

  const navigationSchema = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    itemListElement: [
      ...HeaderLinks.filter((link) => link.route !== "/").map((link, index) => ({
        "@type": "SiteNavigationElement",
        name: link.label,
        url: `https://macroedge.pro/${link.route}`
      }))
    ]
  };

  const softwareSchemaJson = JSON.stringify(softwareSchema);
  const websiteSchemaJson = JSON.stringify(websiteSchema);
  const organizationSchemaJson = JSON.stringify(organizationSchema);
  const navigationSchemaJson = JSON.stringify(navigationSchema);
</script>

<div class="relative w-full overflow-x-hidden">
  <section id="hero" class="w-full"><Hero /></section>
  <section id="why-us" class="w-full"><WhyUs /></section>
  <section id="features" class="w-full"><Features /></section>
  <section id="pricing" class="w-full"><Pricing /></section>
  <section id="faqs" class="w-full"><Faq /></section>
  <section id="contact" class="w-full"><Contact {form} /></section>
</div>

<svelte:head>
  <meta
    name="description"
    content="Dominate the forex market with data-backed trading insights. Real-time risk signals, COT reports, and economic events tracking."
  />
  <meta
    name="keywords"
    content="fx trading, forex market, macroeconomics, economic calendar, COT report, bond yields, market insights, real-time market data, trader platform, institutional tools, trend analysis, market analysis, trading strategies, trading tools, trading signals, trading indicators, smart money concepts, macro edge, macro edge app, macro edge login, macro edge sign in, macro edge dashboard, macro edge trading, macro edge trading platform, macro edge trading tools, macro edge trading indicators, macro edge trading strategies, macro edge trading signals, macro edge trading indicators, macro edge trading strategies, macro edge trading tools, macro edge trading indicators, technical analysis, fundamental analysis, macro edge, macro edge app, macro edge login, macro edge sign in, macro edge dashboard, macro edge trading, macro edge trading platform, macro edge trading tools, macro edge trading indicators, macro edge trading strategies, macro edge trading signals, macro edge trading indicators, macro edge trading strategies, macro edge trading tools, macro edge trading indicators, technical analysis, fundamental analysis"
  />
  <meta name="robots" content="index, follow" />
  <meta name="author" content="Macro Edge" />
  <link rel="canonical" href="https://macroedge.pro" />
  <meta property="og:title" content="Macro Edge" />
  <meta
    property="og:description"
    content="Dominate the forex market with data-backed trading insights. Real-time risk signals, COT reports, and economic events tracking."
  />
  <meta property="og:url" content="https://macroedge.pro" />
  <meta property="og:image" content="https://macroedge.pro/og-en.png" />
  <meta property="og:type" content="website" />
  <meta property="og:site_name" content="Macro Edge" />

  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="Macro Edge" />
  <meta
    name="twitter:description"
    content="Dominate the forex market with data-backed trading insights. Real-time risk signals, COT reports, and economic events tracking."
  />
  <meta name="twitter:image" content="https://macroedge.pro/og-en.png" />
  <!-- <meta name="twitter:site" content="@MacroEdge" /> -->

  {@html `<script type="application/ld+json">${organizationSchemaJson}</script>`}
  {@html `<script type="application/ld+json">${websiteSchemaJson}</script>`}
  {@html `<script type="application/ld+json">${softwareSchemaJson}</script>`}
  {@html `<script type="application/ld+json">${navigationSchemaJson}</script>`}
</svelte:head>
