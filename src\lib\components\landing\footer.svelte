<script lang="ts">
  import { MoveRight } from "lucide-svelte";
  import { HeaderLinks, LegalLinks, SocialLinks } from "$lib/constants/links";
  import Cookies from "$lib/components/cookies.svelte";
  import { goto } from "$app/navigation";
  import { page } from "$app/state";

  export let showCookies: boolean;
  const currentYear = new Date().getFullYear();

  const handleNavClick = async (e: MouseEvent, route: string) => {
    const currentPath = page.url.pathname;

    if (route.startsWith("#")) {
      e.preventDefault();
      const targetId = route.slice(1);
      const el = document.getElementById(targetId);

      if (el) {
        el.scrollIntoView({ behavior: "smooth", block: "start" });
      } else if (currentPath !== "/") {
        await goto("/");

        setTimeout(() => {
          const targetEl = document.getElementById(targetId);
          if (targetEl) {
            targetEl.scrollIntoView({ behavior: "smooth", block: "start" });
          }
        }, 30);
      }
    } else if (route === "/") {
      e.preventDefault();

      if (currentPath !== "/") {
        await goto("/");
      } else {
        window.scrollTo({ top: 0, behavior: "smooth" });
      }
    }
  };
</script>

<footer class="relative w-full overflow-hidden">
  <div class="relative w-full border-y border-white/8 bg-emerald-600/10">
    <div class="mx-auto max-w-7xl px-4 py-16 lg:flex lg:items-center lg:justify-between lg:px-8">
      <h2 class="text-center text-4xl font-bold tracking-tight text-white sm:text-4xl lg:text-left">
        Ready to dive in?<br />Start your free trial today.
      </h2>
      <div class="mt-10 flex items-center justify-center gap-x-6 lg:mt-0 lg:flex-shrink-0">
        <a
          href="/login"
          class="text-md inline-flex w-full items-center justify-center gap-x-1 rounded-lg border border-transparent bg-emerald-600 px-8 py-3 font-medium tracking-tight uppercase hover:bg-emerald-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-500 disabled:pointer-events-none disabled:opacity-50 sm:w-auto"
          >Start Free Trial <MoveRight class="size-5 flex-shrink-0" /></a
        >
      </div>
    </div>
  </div>

  <div class="relative mx-auto w-full max-w-7xl px-4 pt-8 pb-6 lg:px-8">
    <div class="grid grid-cols-1 gap-4 md:grid-cols-3 md:gap-10 lg:grid-cols-4">
      <div>
        <div class="flex flex-col gap-y-4 text-sm text-gray-300">
          <p>
            <a class="inline-flex" href={HeaderLinks[0].route}
              ><img src="/logo.svg" class="w-48 select-none" alt="Macro Edge" /></a
            >
          </p>
          <div class="flex gap-x-2.5">
            {#each SocialLinks as item}
              {@const Icon = item.icon}
              <a
                href={item.route}
                target="_blank"
                class="inline-flex size-10 items-center justify-center gap-x-2 rounded-lg border border-transparent bg-white/10 text-sm font-semibold hover:bg-emerald-500 hover:text-white"
                ><Icon class="size-5 flex-shrink-0" /></a
              >
            {/each}
          </div>

          <p>
            <strong class="uppercase">Macro Edge LTD</strong><br /> Registration Number 16375195<br /> Poole, United Kingdom
          </p>
        </div>
      </div>

      <div>
        <h4 class="text-sm font-medium uppercase">Page Links</h4>
        <div class="mt-2 grid space-y-2 text-sm">
          {#each HeaderLinks as item}
            <p>
              <a
                class="inline-flex gap-x-2 text-gray-300 hover:text-emerald-500"
                href={item.route}
                onclick={(e) => handleNavClick(e, item.route)}
              >
                {item.label}
              </a>
            </p>
          {/each}
        </div>
      </div>

      <div>
        <h4 class="text-sm font-medium uppercase">Legal</h4>
        <div class="mt-2 grid space-y-2 text-sm">
          {#each LegalLinks as item}
            <p>
              <a class="inline-flex gap-x-2 text-gray-300 hover:text-emerald-500" href={item.route}>{item.label}</a>
            </p>
          {/each}

          <p>
            <button
              class="inline-flex cursor-pointer gap-x-2 text-gray-300 hover:text-emerald-500"
              onclick={() => (showCookies = !showCookies)}>Cookies Settings</button
            >
          </p>
        </div>
      </div>
    </div>
    <div class="mt-5 border-t border-white/8 pt-5 sm:mt-8">
      <div class="sm:flex sm:items-center sm:justify-between">
        <div><p class="text-sm text-gray-300">© {currentYear} Macro Edge, All rights reserved.</p></div>
      </div>
    </div>
  </div>

  <div class="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80" aria-hidden="true">
    <div
      class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-emerald-500 to-blue-600 opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
      style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
    ></div>
  </div>
</footer>

<Cookies bind:showCookies />
