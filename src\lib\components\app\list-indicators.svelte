<script lang="ts">
  let props = $props();
  let CurrencyScoreTotal = $state(0);
  let EquityScoreTotal = $state(0);

  $effect(() => {
    const CurrencyScores = props.items.reduce(
      (acc: number, item: any) => acc + item.CurrencyScore,
      0
    );
    const EquityScores = props.items.reduce((acc: number, item: any) => acc + item.EquityScore, 0);
    const CurrencyWeights = props.items.reduce(
      (acc: number, item: any) => acc + item.CurrencyWeight,
      0
    );
    const EquityWeights = props.items.reduce(
      (acc: number, item: any) => acc + item.EquityWeight,
      0
    );

    CurrencyScoreTotal = CurrencyScores / CurrencyWeights;
    EquityScoreTotal = EquityScores / EquityWeights;
  });

  const classCurrencyScore = (value: number) =>
    value >= 0.55 ? "text-green-500" : value <= 0.45 ? "text-red-500" : "text-yellow-500";
  const classEquityScore = (value: number) =>
    value >= 0.55 ? "text-green-500" : value <= 0.45 ? "text-red-500" : "text-yellow-500";

  const filterUnit = (unit: string) => {
    if (unit === "percent" || unit === "percent of GDP" || unit === "percentage points") {
      return "%";
    } else if (unit === "Thousand" || unit === "Persons" || unit === "Thousand Barrels") {
      return "K";
    } else if (
      unit === "Million" ||
      unit === "USD Million" ||
      unit === "Million Units" ||
      unit === "SIPRI TIV Million"
    ) {
      return "MM";
    } else if (
      unit === "USD Billion" ||
      unit === "Billion USD" ||
      unit === "billion cubic feet" ||
      unit === "Billion cubic meters"
    ) {
      return "B";
    } else if (unit === "Trillion USD") {
      return "T";
    } else if (unit === "Thousand Barrels Per Day") {
      return "Kbpd";
    } else if (unit === "Million Acres") {
      return "MAc";
    } else if (unit === "BBL/D/1K") {
      return "BBL/D/1K";
    } else {
      return "";
    }
  };
</script>

<div class="w-full tracking-tighter">
  <div class="flex w-full justify-between gap-2 rounded-md border-white/5 bg-white/5 px-2 py-2.5">
    <div class="flex items-center gap-1">
      <img
        src="/icons/{props.icon}"
        alt={props.title}
        class="size-4 rounded-xs select-none"
        width="16"
        height="16"
      />
      <h2 class="relative text-sm leading-none font-medium uppercase">{props.title}</h2>
    </div>
    {#if CurrencyScoreTotal === 0 || EquityScoreTotal === 0}
      <svg
        class="size-4 animate-spin text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        ><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"
        ></circle><path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path></svg
      >
    {:else}
      <div class="flex items-center gap-2">
        <p class="flex gap-1.5 text-sm leading-none font-medium">
          FX
          <span
            class="h-3 w-3 rounded-xs {CurrencyScoreTotal >= 0.55
              ? `bg-green-500`
              : CurrencyScoreTotal <= 0.45
                ? `bg-red-500`
                : `bg-yellow-500`}"
          ></span>
        </p>
        <p class="flex gap-1.5 text-sm leading-none font-medium">
          EQ
          <span
            class="h-3 w-3 rounded-xs {EquityScoreTotal >= 0.55
              ? `bg-green-500`
              : EquityScoreTotal <= 0.45
                ? `bg-red-500`
                : `bg-yellow-500`}"
          ></span>
        </p>
        <!-- <p class="text-sm leading-none">
          FX<span
            class="ml-1 {classCurrencyScore(CurrencyScoreTotal)}
          ">{CurrencyScoreTotal.toFixed(2)}</span
          >
        </p>
        <p class="text-sm leading-none">
          EQ<span class="ml-1 {classEquityScore(EquityScoreTotal)}"
            >{EquityScoreTotal.toFixed(2)}</span
          >
        </p> -->
      </div>
    {/if}
  </div>
  <div class="-mx-1.5 w-full overflow-y-auto">
    <table class="w-full table-fixed tracking-tighter">
      <thead class="text-right text-xs uppercase">
        <tr>
          <th class="w-3/6 truncate p-2 text-left leading-none font-medium"></th>
          <th class="w-1/6 truncate p-2 leading-none font-medium">Actual</th>
          <th class="w-1/6 truncate p-2 leading-none font-medium">Previous</th>
        </tr>
      </thead>
      <tbody class="text-right text-sm">
        {#each props.items as item}
          <tr>
            <td class="truncate px-2 py-1.5 text-left leading-none font-normal">{item.name}</td>
            <td class="px-2 py-1.5 leading-none font-normal"
              >{item.actual}{filterUnit(item.unit)}</td
            >
            <td class="px-2 py-1.5 leading-none font-normal"
              >{item.previous}{filterUnit(item.unit)}</td
            >
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</div>
