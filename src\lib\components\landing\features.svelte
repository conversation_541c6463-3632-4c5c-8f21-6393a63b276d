<script lang="ts">
  import { Check, Square<PERSON>heckBig, CircleCheckBig } from "lucide-svelte";
</script>

<div class="relative">
  <div class="mx-auto max-w-7xl px-4 lg:px-8">
    <div class="relative space-y-5 py-16">
      <div
        class="mx-auto grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:max-w-none lg:grid-cols-2"
      >
        <div class="lg:pt-4 lg:pr-8">
          <div class="lg:max-w-lg">
            <h2 class="text-3xl font-bold tracking-tight text-white sm:text-4xl">Market Risk</h2>
            <p class="mt-2 text-lg leading-8 text-gray-300">
              Track risk sentiment with live stocks, indices, bonds yields, and safe-haven assets.
            </p>
            <dl class="mt-8 max-w-xl space-y-4 text-base leading-7 text-gray-300 lg:max-w-none">
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> US Indices
                </dt>
                <dd class="inline">S&P 500, NASDAQ, Dow Jones.</dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Top 10 Stocks
                </dt>
                <dd class="inline">Dow Jones & NASDAQ.</dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Risk-On/Risk-Off
                </dt>
                <dd class="inline">
                  Metrics like VIX, Dollar, Yen Japanese, Franc Swiss, US Bonds Yields.
                </dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> US Bonds
                  Yields Curve
                </dt>
                <dd class="inline">Multiple Years.</dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Economic
                  Calendar
                </dt>
                <dd class="inline">Economic indicators releases and High-Impact Events.</dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Market Signal
                </dt>
                <dd class="inline">
                  Real-time calculations predicting daily market direction based on multiple assets
                  correlations.
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <div>
          <img
            src="/views/market-risk.png"
            alt="Feature"
            class="w-[48rem] max-w-none rounded-md shadow-xl ring-1 ring-white/10 select-none sm:w-[57rem] md:-ml-4 lg:-ml-0"
          />
        </div>
      </div>

      <div
        class="absolute inset-x-0 top-4 -z-10 flex transform-gpu justify-center overflow-hidden blur-3xl"
        aria-hidden="true"
      >
        <div
          class="aspect-[1108/632] w-[69.25rem] flex-none bg-gradient-to-r from-emerald-500 to-blue-600 opacity-25"
          style="clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%)"
        ></div>
      </div>
    </div>

    <div class="relative space-y-5 py-16">
      <div
        class="mx-auto grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:max-w-none lg:grid-cols-2"
      >
        <div class="lg:pt-4 lg:pr-8">
          <div class="lg:max-w-lg">
            <h2 class="text-3xl font-bold tracking-tight text-white sm:text-4xl">Global Market</h2>
            <p class="mt-2 text-lg leading-8 text-gray-300">
              Comprehensive view of global stock indices, currencies, bond yields, and commodity
              prices.
            </p>
            <dl class="mt-8 max-w-xl space-y-4 text-base leading-7 text-gray-300 lg:max-w-none">
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Indices
                </dt>
                <dd class="inline">America, Europe and Asia.</dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Currencies
                </dt>
                <dd class="inline">Dollar, Emerging Markets.</dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Bond Yields
                </dt>
                <dd class="inline">US, Germany, Japan, Brazil and Mexican.</dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Commodity
                </dt>
                <dd class="inline">Metals, Energy and Agriculture.</dd>
              </div>
            </dl>
          </div>
        </div>

        <div class="flex items-start justify-end lg:order-first">
          <img
            src="/views/macro-economic.png"
            alt="Feature"
            class="w-[48rem] max-w-none rounded-md shadow-xl ring-1 ring-white/10 select-none sm:w-[57rem] md:-ml-4 lg:-ml-0"
          />
        </div>
      </div>

      <div
        class="absolute inset-x-0 top-4 -z-10 flex transform-gpu justify-center overflow-hidden blur-3xl"
        aria-hidden="true"
      >
        <div
          class="aspect-[1108/632] w-[69.25rem] flex-none bg-gradient-to-r from-emerald-500 to-blue-600 opacity-25"
          style="clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%)"
        ></div>
      </div>
    </div>

    <div class="relative space-y-5 py-16">
      <div
        class="mx-auto grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:max-w-none lg:grid-cols-2"
      >
        <div class="lg:pt-4 lg:pr-8">
          <div class="lg:max-w-lg">
            <h2 class="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Economic Calendar
            </h2>
            <p class="mt-2 text-lg leading-8 text-gray-300">
              Real-time economic indicators, event impacts, and market-moving releases.
            </p>
            <dl class="mt-8 max-w-xl space-y-4 text-base leading-7 text-gray-300 lg:max-w-none">
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Live Event
                  Feed
                </dt>
                <dd class="inline"></dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Scheduled
                  Economic Releases & Forecasts
                </dt>
                <dd class="inline"></dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Instant Data
                  Updates
                </dt>
                <dd class="inline"></dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> High-Impact
                  Event Alerts
                </dt>
                <dd class="inline"></dd>
              </div>
            </dl>
          </div>
        </div>

        <div>
          <img
            src="/views/economic-calendar.png"
            alt="Feature"
            class="w-[48rem] max-w-none rounded-md shadow-xl ring-1 ring-white/10 select-none sm:w-[57rem] md:-ml-4 lg:-ml-0"
          />
        </div>
      </div>

      <div
        class="absolute inset-x-0 top-4 -z-10 flex transform-gpu justify-center overflow-hidden blur-3xl"
        aria-hidden="true"
      >
        <div
          class="aspect-[1108/632] w-[69.25rem] flex-none bg-gradient-to-r from-emerald-500 to-blue-600 opacity-25"
          style="clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%)"
        ></div>
      </div>
    </div>

    <div class="relative space-y-5 py-16">
      <div
        class="mx-auto grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:max-w-none lg:grid-cols-2"
      >
        <div class="lg:pt-4 lg:pr-8">
          <div class="lg:max-w-lg">
            <h2 class="text-3xl font-bold tracking-tight text-white sm:text-4xl">COT Report</h2>
            <p class="mt-2 text-lg leading-8 text-gray-300">
              Insights on trader positioning, net positions, and speculative vs. hedging activity.
            </p>
            <dl class="mt-8 max-w-xl space-y-4 text-base leading-7 text-gray-300 lg:max-w-none">
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Commercial
                  & Non-Commercial
                </dt>
                <dd class="inline"></dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Net Positions
                  & COT Index
                </dt>
                <dd class="inline"></dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Long and
                  Short Positions
                </dt>
                <dd class="inline"></dd>
              </div>
              <div class="relative pl-9">
                <dt class="inline font-semibold text-white">
                  <CircleCheckBig class="absolute top-1 left-1 h-5 w-5 text-emerald-500" /> Sector-Based
                  Analysis
                </dt>
                <dd class="inline"></dd>
              </div>
            </dl>
          </div>
        </div>

        <div class="flex items-start justify-end lg:order-first">
          <img
            src="/views/cot-report-id.png"
            alt="Feature"
            class="w-[48rem] max-w-none rounded-md shadow-xl ring-1 ring-white/10 select-none sm:w-[57rem] md:-ml-4 lg:-ml-0"
          />
        </div>
      </div>

      <div
        class="absolute inset-x-0 top-4 -z-10 flex transform-gpu justify-center overflow-hidden blur-3xl"
        aria-hidden="true"
      >
        <div
          class="aspect-[1108/632] w-[69.25rem] flex-none bg-gradient-to-r from-emerald-500 to-blue-600 opacity-25"
          style="clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%)"
        ></div>
      </div>
    </div>
  </div>
</div>
