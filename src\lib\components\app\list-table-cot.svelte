<script lang="ts">
  let props = $props();
  const formatNet = (net: number) => (net > 0 ? "text-green-500" : net < 0 ? "text-red-500" : "");
  const formatLong = (long: number) =>
    long > 0 ? "text-green-500" : long < 0 ? "text-red-500" : "";
  const formatShort = (short: number) =>
    short > 0 ? "text-green-500" : short < 0 ? "text-red-500" : "";
  const formatDate = (date: string) =>
    new Date(date).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit"
    });
</script>

<div class="-m-1.5 overflow-x-auto">
  <div class="inline-block min-w-full p-1.5 align-middle">
    <div class="w-full overflow-hidden">
      <table class="w-full table-fixed tracking-tight">
        <thead>
          <tr>
            <th
              class="w-2/12 overflow-hidden rounded-l-md border-white/5 bg-white/5 py-2.5 pl-2 text-center text-sm leading-none font-medium text-ellipsis whitespace-nowrap uppercase"
              >{props.title}</th
            >
            <th
              class="w-1/12 border-white/5 bg-white/5 py-2.5 text-center text-xs leading-none font-medium uppercase"
              >Net</th
            >
            <th
              class="w-1/12 border-white/5 bg-white/5 py-2.5 text-center text-xs leading-none font-medium uppercase"
              >Change</th
            >
            <th
              class="w-1/12 border-white/5 bg-white/5 py-2.5 text-center text-xs leading-none font-medium uppercase"
              >Long</th
            >
            <th
              class="w-1/12 border-white/5 bg-white/5 py-2.5 text-center text-xs leading-none font-medium uppercase"
              >Change</th
            >
            <th
              class="w-1/12 border-white/5 bg-white/5 py-2.5 text-center text-xs leading-none font-medium uppercase"
              >Short</th
            >
            <th
              class="w-1/12 rounded-r-md border-white/5 bg-white/5 py-2.5 pr-2 text-center text-xs leading-none font-medium uppercase"
              >Change</th
            >
          </tr>
        </thead>
        <tbody>
          {#each props.items as item}
            <tr>
              <td class="rounded-l-md py-0.5 text-center text-sm whitespace-nowrap md:px-1"
                >{formatDate(item.date)}</td
              >
              <td class="py-0.5 text-center text-sm whitespace-nowrap md:px-1 {formatNet(item.net)}"
                >{item.net}</td
              >
              <td
                class="py-0.5 text-center text-sm whitespace-nowrap md:px-1 {formatNet(
                  item.changeNet
                )}"
                >{item.changeNet > 0 ? "+" : ""}{item.changeNet}
              </td>
              <td class="py-0.5 text-center text-sm whitespace-nowrap md:px-1">{item.long} </td>
              <td
                class="py-0.5 text-center text-sm whitespace-nowrap md:px-1 {formatLong(
                  item.changeLong
                )}"
                >{item.changeLong > 0 ? "+" : ""}{item.changeLong}
              </td>
              <td class="py-0.5 text-center text-sm whitespace-nowrap md:px-1">{item.short} </td>
              <td
                class="rounded-r-md py-0.5 text-center text-sm whitespace-nowrap md:px-1 {formatShort(
                  item.changeShort
                )}"
                >{item.changeShort > 0 ? "+" : ""}{item.changeShort}
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  </div>
</div>
