import { fail } from "@sveltejs/kit";
import type { Actions } from "./$types";
import { services } from "$lib/server/services";
import { contactTemplate } from "$lib/server/email";
import { useRateLimit } from "$lib/server/hooks/useRateLimit";
import { useRecaptcha } from "$lib/server/hooks/useRecaptcha";

export const actions: Actions = {
  submit: async ({ request, getClientAddress, fetch }) => {
    const formData = await request.formData();
    const firstname = formData.get("firstname")?.toString();
    const lastname = formData.get("lastname")?.toString();
    const email = formData.get("email")?.toString();
    const message = formData.get("message")?.toString();
    const recaptchaToken = formData.get("recaptcha_token")?.toString();
    const userAgent = request.headers.get("user-agent");
    const ipAddress = getClientAddress();

    const recaptchaResult = await useRecaptcha({
      token: recaptchaToken!,
      ipAddress: ipAddress,
      userAgent: userAgent,
      fetch
    });

    if (!recaptchaResult.isValid) {
      return fail(400, { error: "Validation failed." });
    }

    if (await useRateLimit(`support:${ipAddress}`, 3, 60 * 60 * 24)) {
      return fail(429, { error: "You have reached the daily limit." });
    }

    if (!firstname) return fail(400, { error: "First Name is required." });
    if (!lastname) return fail(400, { error: "Last Name is required." });
    if (!email) return fail(400, { error: "Email is required." });
    if (!email.includes("@")) return fail(400, { error: "Email is invalid." });
    if (!message) return fail(400, { error: "Message is required." });
    if (message.length < 10) return fail(400, { error: "Message must be at least 10 characters." });

    function sanitize(message: string) {
      const escaped = message
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
      return escaped.replace(/\n/g, "<br>");
    }

    const dataForm = {
      firstname: firstname!,
      lastname: lastname!,
      email: email!,
      message: sanitize(message)!
    };

    const html = await contactTemplate({ data: dataForm });
    const text = `${firstname} ${lastname} - Support\n\n${message}`;

    const resend = services.resend();
    const { error } = await resend.emails.send({
      from: `Support <<EMAIL>>`,
      to: "<EMAIL>",
      replyTo: email,
      subject: `${firstname} ${lastname} - Support`,
      html,
      text
    });

    if (error) return fail(400, { error: "Message was not sent" });

    return { success: true, text: "Message was sent successfully" };
  }
};
