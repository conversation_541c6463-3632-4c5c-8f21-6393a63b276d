export async function useRecaptcha({
  token,
  ipAddress,
  userAgent,
  expectedAction = "submit",
  fetch
}: {
  token: string | null;
  ipAddress: string | null;
  userAgent: string | null;
  expectedAction?: string;
  fetch: (input: RequestInfo, init?: RequestInit) => Promise<Response>;
}) {
  const RECAPTCHA_PROJECT_ID = "macroedge";
  const RECAPTCHA_SITE_KEY = "6Lf3UiQrAAAAAOI328aI1jf_-Ak6XN8druAVbqYH";
  const RECAPTCHA_API_KEY = "AIzaSyAARlxoEqcq0r0LL4ch_72oFX8IsTp_9Tk";

  const response = await fetch(
    `https://recaptchaenterprise.googleapis.com/v1/projects/${RECAPTCHA_PROJECT_ID}/assessments?key=${RECAPTCHA_API_KEY}`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        event: {
          token,
          siteKey: RECAPTCHA_SITE_KEY,
          expectedAction,
          userIpAddress: ipAddress,
          userAgent
        }
      })
    }
  );

  const data = await response.json();

  const isValid =
    data?.tokenProperties?.valid &&
    data?.tokenProperties?.action === expectedAction &&
    (data?.riskAnalysis?.score ?? 0) >= 0.5;

  return { isValid, score: data?.riskAnalysis?.score ?? 0, raw: data };
}
