import { json } from "@sveltejs/kit";
import type { RequestHandler } from "@sveltejs/kit";

export const GET: RequestHandler = async () => {
  try {
    const response = await fetch(`https://investing-calendar.fly.dev/earnings`);
    const fetched = await response.json();

    if (!response.ok || !fetched || fetched.error) {
      return json({ error: "Error fetching data" }, { status: 400 });
    }

    return json({ data: fetched.data }, { status: 200 });
  } catch (error) {
    return json({ error: error }, { status: 500 });
  }
};
