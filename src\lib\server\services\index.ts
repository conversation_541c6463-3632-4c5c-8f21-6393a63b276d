import { lazy } from "$lib/utils";
import { getDbClient } from "$lib/server/db";
import { getStripeClient } from "$lib/server/services/stripe";
import { getResendClient } from "$lib/server/services/resend";
import { getRedisClient } from "$lib/server/services/redis";
import { getOpenaiClient } from "$lib/server/services/openai";

export const services = {
  db: lazy(() => getDbClient()),
  stripe: lazy(() => getStripeClient()),
  resend: lazy(() => getResendClient()),
  redis: lazy(() => getRedisClient()),
  openai: lazy(() => getOpenaiClient())
};
