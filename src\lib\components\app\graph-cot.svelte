<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import Chart, { type ChartItem, type ChartConfiguration } from "chart.js/auto";

  let props = $props();
  let chartElement: ChartItem;
  let chartInstance: Chart | null = null;

  let labels = [...(props.labels ?? [])].reverse();
  let items = {
    commercial: [...(props.items.commercial ?? [])].reverse(),
    nonCommercial: [...(props.items.nonCommercial ?? [])].reverse()
  };

  const formatDate = (date: string) =>
    new Date(date).toLocaleDateString("en-GB", { year: "2-digit", month: "2-digit", day: "2-digit" });

  const chartData = {
    labels,
    datasets: [
      {
        label: "Commercial",
        data: items.commercial,
        borderColor: "#2b7fff",
        backgroundColor: "#2b7fff",
        borderWidth: 2,
        pointRadius: 2,
        fill: false
        // stepped: (props.title === "COT Index"),
      },
      {
        label: "Non Commercial",
        data: items.nonCommercial,
        borderColor: "#fb2c36",
        backgroundColor: "#fb2c36",
        borderWidth: 2,
        pointRadius: 2,
        fill: false
        // stepped: (props.title === "COT Index"),
      }
    ]
  };

  const config: ChartConfiguration = {
    type: "line",
    data: chartData,
    options: {
      responsive: true,
      interaction: { mode: "index", intersect: false },
      animation: { duration: 1000 },
      plugins: {
        legend: {
          display: true,
          labels: {
            color: "hsl(0 0% 100%)",
            boxWidth: 12,
            boxHeight: 8,
            usePointStyle: true,
            pointStyle: "rectRounded"
          },
          position: "bottom"
        },
        tooltip: {
          enabled: true,
          mode: "index",
          intersect: false,
          usePointStyle: true,
          backgroundColor: "rgba(17, 24, 39, 0.95)",
          titleColor: "hsl(0 0% 100%)",
          bodyColor: "hsl(0 0% 100%)",
          displayColors: true,
          borderColor: "rgba(17, 24, 39, 0.95)",
          borderWidth: 1,
          caretSize: 0,
          cornerRadius: 5,
          callbacks: {
            labelPointStyle: () => {
              return { pointStyle: "rectRounded", rotation: 0 };
            }
          }
        }
      },
      elements: {
        point: {
          radius: 0,
          borderWidth: 2,
          hoverRadius: 5,
          hoverBorderWidth: 2,
          pointStyle: "circle"
        }
      },
      scales: {
        x: {
          display: true,
          type: "category",
          ticks: {
            display: false
          },
          grid: {
            drawTicks: false,
            drawOnChartArea: false,
            color: "rgba(229, 231, 235, 0.05)"
          }
        },
        x2: {
          type: "category",
          ticks: {
            autoSkip: false,
            maxRotation: 0,
            color: "hsl(0 0% 100%)",
            callback: function (_, index) {
              const date = new Date(labels[index]);
              const month = date.toLocaleString("en-US", { month: "short" });
              const year = date.toLocaleString("en-US", { year: "2-digit" });

              // Show month name only on the first occurrence in that month
              if (index === 0 || new Date(labels[index - 1]).getMonth() !== date.getMonth()) {
                return `${month} ${year}`;
              }
              return "";
            }
          },
          grid: {
            drawOnChartArea: false,
            color: "rgba(229, 231, 235, 0.05)"
          }
        },
        // x: { display: true, title: { display: false }, grid: { color: "rgba(229, 231, 235, 0.05)" }, ticks: { color: "hsl(0 0% 100%)" } },
        y: {
          display: true,
          title: { display: false },
          grid: { color: "rgba(229, 231, 235, 0.05)" },
          ticks: { color: "hsl(0 0% 100%)" }
        }
      }
    },
    plugins: [
      {
        id: "horizontalLine",
        beforeDraw: (chart) => {
          if (props.title !== "COT Index") return;

          const { ctx, scales } = chart;
          const yAxis = scales.y;
          const chartArea = chart.chartArea;

          ctx.save();
          ctx.strokeStyle = "white";
          ctx.lineWidth = 1;
          // ctx.setLineDash([5, 5]); // Dashed line

          const drawLine = (yValue: number) => {
            const y = yAxis.getPixelForValue(yValue);
            ctx.strokeStyle = yValue === 80 ? "#00c951" : "#fb2c36";
            ctx.beginPath();
            ctx.moveTo(chartArea.left, y);
            ctx.lineTo(chartArea.right, y);
            ctx.stroke();
          };

          drawLine(20);
          drawLine(80);
          ctx.restore();
        }
      }
    ]
  };

  $effect(() => {
    labels = [...(props.labels ?? [])].reverse();
    items = {
      commercial: [...(props.items?.commercial ?? [])].reverse(),
      nonCommercial: [...(props.items?.nonCommercial ?? [])].reverse()
    };

    if (chartInstance) {
      chartInstance.data.labels = labels;
      chartInstance.data.datasets[0].data = items.commercial;
      chartInstance.data.datasets[1].data = items.nonCommercial;
      chartInstance.update();
    }
  });

  onMount(() => {
    chartInstance = new Chart(chartElement, config);
  });

  onDestroy(() => {
    if (chartInstance) chartInstance.destroy();
  });
</script>

<div class="w-full space-y-2">
  <div class="w-full tracking-tighter">
    <div class="flex w-full justify-between gap-2 rounded-md border-white/5 bg-white/5 px-2 py-2.5">
      <h2 class="text-sm leading-none font-medium uppercase">{props.title}</h2>
    </div>
  </div>

  <div class="w-full">
    <canvas class="w-full select-none" height="250" bind:this={chartElement}></canvas>
  </div>
</div>
