<script lang="ts">
  import SockJS from "sockjs-client";
  import { onMount, onDestroy } from "svelte";
  import { afterNavigate } from "$app/navigation";
  import ListAverage from "$lib/components/app/list-average.svelte";
  import ListTable from "$lib/components/app/list-table.svelte";
  import ListEarnings from "$lib/components/app/list-earnings.svelte";
  import ListCalendar from "$lib/components/app/list-calendar.svelte";
  import ListSession from "$lib/components/app/list-session.svelte";
  import ListCot from "$lib/components/app/list-cot.svelte";
  import ListIndicators from "$lib/components/app/list-indicators.svelte";
  import { us, dowjones, nasdaq, risk, bonds, idx, yxd, cot } from "$lib/constants/market-risk";
  import {
    USDIndicator,
    EURIndicator,
    GBPIndicator,
    AUDIndicator,
    CADIndicator,
    JPYIndicator,
    CHFIndicator
  } from "$lib/constants/economic-indicators";
  import calcRisk from "$lib/utils/calc-risk";

  let USIndicator = $state(USDIndicator);
  let EUIndicator = $state(EURIndicator);
  let GBIndicator = $state(GBPIndicator);
  let AUIndicator = $state(AUDIndicator);
  let CAIndicator = $state(CADIndicator);
  let JPIndicator = $state(JPYIndicator);
  let CHIndicator = $state(CHFIndicator);

  let USAvg: any = $state(null);
  let DJAvg: any = $state(null);
  let NQAvg: any = $state(null);
  let RISKAvg: any = $state(null);

  let usData = $state(us);
  let djData = $state(dowjones);
  let nqData = $state(nasdaq);
  let riskData = $state(risk);
  let bondsData = $state(bonds);
  let idxData = $state(idx);
  let yxdData = $state(yxd);
  let cotData = $state(cot);
  let cotDate: any = $state(null);

  type CalendarEvent = {
    id: string;
    time: number | string | null;
    flag: string | null;
    currency: string | null;
    importance: number | null;
    event: string;
    actual: string | null;
    forecast: string | null;
    previous: string | null;
    signal: string | null;
    localDate?: Date | null;
  };

  let calendarData: CalendarEvent[] = $state([]);
  let calendarIDs: string[] = $state([]);
  let calendarTimers: Record<string, NodeJS.Timeout[]> = {};

  let earningsData: any = $state([]);
  let earningsIDs: string[] = $state([]);
  let showEarnings = $state(false);

  let sock: any;
  let TimeZoneID = 55;
  let reconnectInterval = 5000;
  let heartbeatInterval = 25000;
  let isConnected = $state(false);
  let heartbeatTimer: ReturnType<typeof setInterval> | undefined;

  let tokyoStatus = $state("00:00:00");
  let londonStatus = $state("00:00:00");
  let newyorkStatus = $state("00:00:00");
  let nyseStatus = $state("00:00:00");
  let interval: ReturnType<typeof setInterval> | undefined;

  function formatCountdown(ms: number) {
    if (ms <= 0) return "00:00:00";
    const totalSeconds = Math.floor(ms / 1000);
    const hrs = String(Math.floor(totalSeconds / 3600)).padStart(2, "0");
    const mins = String(Math.floor((totalSeconds % 3600) / 60)).padStart(2, "0");
    const secs = String(totalSeconds % 60).padStart(2, "0");
    return `${hrs}:${mins}:${secs}`;
  }

  function getNewYorkTimeInSeconds(): number {
    const formatter = new Intl.DateTimeFormat("en-US", {
      timeZone: "America/New_York",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false
    });

    const parts = formatter.formatToParts(new Date());

    const h = parseInt(parts.find((p) => p.type === "hour")?.value ?? "0");
    const m = parseInt(parts.find((p) => p.type === "minute")?.value ?? "0");
    const s = parseInt(parts.find((p) => p.type === "second")?.value ?? "0");

    return h * 3600 + m * 60 + s;
  }

  function updateProgressAndCountdown() {
    const nyDate = new Date().toLocaleString("en-US", { timeZone: "America/New_York" });
    const nyDay = new Date(nyDate).getDay();

    if (nyDay === 0 || nyDay === 6) {
      tokyoStatus = "CLOSED";
      londonStatus = "CLOSED";
      newyorkStatus = "CLOSED";
      nyseStatus = "CLOSED";
      return;
    }

    const nowInNY = getNewYorkTimeInSeconds();

    // Tokyo session time
    const tokyoStart = 20 * 3600;
    const tokyoClose = 5 * 3600;

    // London session time
    const londonStart = 3 * 3600;
    const londonClose = 12 * 3600;

    // New York session times
    const newyorkStart = 8 * 3600;
    const nyseStart = 9 * 3600 + 30 * 60;
    const newyorkClose = 17 * 3600;

    const untilTokyo =
      nowInNY < tokyoStart ? tokyoStart - nowInNY : 24 * 3600 - nowInNY + tokyoStart;
    const untilLondon = londonStart - nowInNY;
    const untilNewyork = newyorkStart - nowInNY;
    const untilNyse = nyseStart - nowInNY;

    // Tokyo status
    const isTokyoOpen = nowInNY >= tokyoStart || nowInNY < tokyoClose;
    if (isTokyoOpen) {
      tokyoStatus = "OPEN";
    } else {
      tokyoStatus = formatCountdown(untilTokyo * 1000);
    }

    // London status
    if (nowInNY < londonStart) {
      londonStatus = formatCountdown(untilLondon * 1000);
    } else if (nowInNY >= londonStart && nowInNY < londonClose) {
      londonStatus = "OPEN";
    } else {
      londonStatus = "CLOSED";
    }

    // New York status
    if (nowInNY < newyorkStart) {
      newyorkStatus = formatCountdown(untilNewyork * 1000);
    } else if (nowInNY >= newyorkStart && nowInNY < newyorkClose) {
      newyorkStatus = "OPEN";
    } else {
      newyorkStatus = "CLOSED";
    }

    // NYSE status
    if (nowInNY < nyseStart) {
      nyseStatus = formatCountdown(untilNyse * 1000);
    } else if (nowInNY >= nyseStart && nowInNY < newyorkClose) {
      nyseStatus = "OPEN";
    } else {
      nyseStatus = "CLOSED";
    }
  }

  const startSockJS = () => {
    sock = new SockJS("https://streaming.forexpros.com/echo");

    sock.onopen = () => {
      isConnected = true;
      startHeartbeat();

      const allIDs = [...us, ...dowjones, ...nasdaq, ...risk, ...bonds].map((item) => item.id);

      allIDs.forEach((id) => {
        if (sock && sock.readyState === SockJS.OPEN) {
          sock.send(
            JSON.stringify({ _event: "subscribe", tzID: TimeZoneID, message: `pid-${id}:` })
          );
        }
      });

      if (calendarIDs.length === 0) {
        fetchCalendar();
      } else {
        calendarIDs.forEach((id) => {
          if (sock && sock.readyState === SockJS.OPEN) {
            sock.send(
              JSON.stringify({ _event: "subscribe", tzID: TimeZoneID, message: `event-${id}:` })
            );
          }
        });
      }

      if (earningsIDs.length === 0) {
        fetchEarnings();
      } else {
        earningsIDs.forEach((id) => {
          if (sock && sock.readyState === SockJS.OPEN) {
            sock.send(
              JSON.stringify({
                _event: "subscribe",
                tzID: TimeZoneID,
                message: `EarningsCal-${id}:`
              })
            );
          }
        });
      }
    };

    sock.onmessage = (e: { data: string }) => {
      try {
        const data = JSON.parse(e.data);

        if (typeof data === "object" && data !== null) {
          data._event = data._event || "tick";
        }

        if (data._event === "tick" && data.message) {
          streamData(data.message.split("::")[1]);
        }
      } catch (err) {}
    };

    sock.onclose = (event: { reason: any; code: number }) => {
      stopHeartbeat();
      isConnected = false;

      if (event.code !== 1000) {
        reconnectSockJS();
      }
    };

    sock.onerror = (error: any) => {
      if (sock) sock.close();
    };
  };

  const startHeartbeat = () => {
    heartbeatTimer = setInterval(() => {
      if (sock && sock.readyState === SockJS.OPEN) {
        sock.send(JSON.stringify({ _event: "heartbeat", data: "h" }));
      }
    }, heartbeatInterval);
  };

  const stopHeartbeat = () => {
    clearInterval(heartbeatTimer);
  };

  const reconnectSockJS = () => {
    stopHeartbeat();
    sock = null;

    setTimeout(() => {
      startSockJS();
    }, reconnectInterval);
  };

  const streamEarnings = (message: any) => {
    const data = JSON.parse(message);

    earningsData = earningsData.map((item: any) =>
      item.id === data.earning_ID
        ? {
            ...item,
            epsActual: data.epsActual ?? item.epsActual,
            epsForecast: data.epsForecast ?? item.epsForecast
          }
        : item
    );
  };

  const streamCalendar = (message: any) => {
    const data = JSON.parse(message);

    calendarData = calendarData.map((item: any) =>
      item.id === data.event_ID
        ? {
            ...item,
            actual: data.actual ?? item.actual,
            forecast: data.forecast ?? item.forecast,
            previous: data.previous ?? item.previous
          }
        : item
    );

    fetchCalendar();
  };

  const streamData = (message: any) => {
    const data = JSON.parse(message);

    if (data.pid) {
      const id = parseInt(data.pid);
      const price = data.last_numeric;
      const day = parseFloat(data.pcp.replace("%", ""));

      const categories = [
        { category: "us", data: usData },
        { category: "nasdaq", data: nqData },
        { category: "dowjones", data: djData },
        { category: "risk", data: riskData },
        { category: "bonds", data: bondsData }
      ];

      categories.forEach(({ category, data }) => {
        const itemIndex = data.findIndex((item: any) => item.id === id);
        if (itemIndex !== -1) {
          updateArray(category, id, price, day);
          fetchRisk();
        }
      });
    } else if (data.event_ID) {
      streamCalendar(message);
    } else {
      console.log("Earnings", data);
      // streamEarnings(message);
    }
  };

  const fetchData = async (id: number, category: string, retryCount = 0) => {
    const period = "P1W";
    const interval = "P1D";

    try {
      const res = await fetch(
        `https://api.investing.com/api/financialdata/${id}/historical/chart?period=${period}&interval=${interval}&pointscount=60`
      );

      if (res.ok) {
        const direct = await res.json();
        if (direct && direct.data && direct.data.length >= 2) {
          let prev = direct.data[direct.data.length - 2][4];
          if (id === 1910 && prev > 0 && prev < 0.1) prev = prev * 100;
          const last = direct.data[direct.data.length - 1][4];
          const day = ((last - prev) / prev) * 100;
          updateArray(category, id, last, day);
          return;
        }
      }

      throw new Error("");
    } catch (error) {
      try {
        const response = await fetch(`/api/market?id=${id}&period=${period}&interval=${interval}`);
        const data = await response.json();

        if (response.ok && data?.price != null && data?.day != null) {
          updateArray(category, id, data.price, data.day);
        } else if (retryCount < 10) {
          return fetchData(id, category, retryCount + 1);
        }
      } catch (err) {
        if (retryCount < 10) {
          return fetchData(id, category, retryCount + 1);
        }
      }
    }
  };

  const fetchAllData = async () => {
    const allCategories = [
      { category: "us", data: usData },
      { category: "nasdaq", data: nqData },
      { category: "dowjones", data: djData },
      { category: "risk", data: riskData },
      { category: "bonds", data: bondsData }
    ];

    const fetchPromises = allCategories.map(({ category, data }) => {
      return Promise.all(data.map((item: any) => fetchData(item.id, category)));
    });

    await Promise.all(fetchPromises);

    fetchRisk();
    startSockJS();
  };

  const fetchRisk = async () => {
    let DXY = 0,
      VIX = 0,
      US10Y = 0,
      US02Y = 0,
      EU10Y = 0,
      GB10Y = 0,
      AU10Y = 0,
      CA10Y = 0,
      JP10Y = 0,
      CH10Y = 0,
      TOP10NQ = 0,
      TOP10DJ = 0;

    const allCategories = [
      { category: "us", data: usData },
      { category: "nasdaq", data: nqData },
      { category: "dowjones", data: djData },
      { category: "risk", data: riskData },
      { category: "bonds", data: bondsData }
    ];

    await Promise.all(
      allCategories.map(({ category, data }) => {
        if (nyseStatus === "OPEN" || nyseStatus === "CLOSED") {
          if (category === "nasdaq") TOP10NQ = NQAvg;
          if (category === "dowjones") TOP10DJ = DJAvg;
        }

        data.forEach((item: any) => {
          if (item.id === 942611) DXY = item.day;
          if (item.id === 44336) VIX = item.day;
          if (item.id === 23705) US10Y = item.day;
          if (item.id === 23701) US02Y = item.day;
          if (item.id === 23693) EU10Y = item.day;
          if (item.id === 23673) GB10Y = item.day;
          if (item.id === 23878) AU10Y = item.day;
          if (item.id === 25275) CA10Y = item.day;
          if (item.id === 23901) JP10Y = item.day;
          if (item.id === 23722) CH10Y = item.day;
        });
      })
    );

    const allRisk = [
      { category: "idx", data: idxData },
      { category: "yxd", data: yxdData }
    ];

    await Promise.all(
      allRisk.map(({ category, data }) => {
        const updatedData = data.map((item: any) => {
          const day = calcRisk(
            item.name,
            DXY,
            VIX,
            US10Y,
            US02Y,
            EU10Y,
            GB10Y,
            AU10Y,
            CA10Y,
            JP10Y,
            CH10Y,
            TOP10NQ,
            TOP10DJ
          );
          return { ...item, day };
        });

        if (category === "idx") idxData = updatedData;
        if (category === "yxd") yxdData = updatedData;
      })
    );
  };

  const updateArray = (category: string, id: number, price: number, day: number) => {
    let array;

    if (category === "us") {
      array = changeData(usData, id, price, day);
      usData = array;
      USAvg = calcAvg(usData);
    } else if (category === "dowjones") {
      array = changeData(djData, id, price, day);
      djData = array;
      DJAvg = calcAvg(djData);
    } else if (category === "nasdaq") {
      array = changeData(nqData, id, price, day);
      nqData = array;
      NQAvg = calcAvg(nqData);
    } else if (category === "risk") {
      array = changeData(riskData, id, price, day);
      riskData = array;
      RISKAvg = calcAvgRisk(riskData);
    } else if (category === "bonds") {
      array = changeData(bondsData, id, price, day);
      bondsData = array;
    }
  };

  const changeData = (array: any, id: number, price: number, day: number) => {
    return array.map((item: any) => {
      if (item.id === id) {
        return { ...item, price, day };
      }
      return item;
    });
  };

  const calcAvg = (data: any[]) => {
    if (data.length === 0) return 0;
    const sum = data.reduce((acc, item) => acc + (item.day || 0), 0);
    return sum / data.length;
  };

  const riskWeights: Record<string, { weight: number; correlation: number }> = {
    VIX: { weight: 30, correlation: 1 },
    USD: { weight: 20, correlation: 1 },
    JPY: { weight: 10, correlation: 1 },
    CHF: { weight: 5, correlation: 1 },
    GOLD: { weight: 3, correlation: 1 },
    BRENT: { weight: 5, correlation: -1 },
    "US 02Y": { weight: 15, correlation: -1 },
    "US 10Y": { weight: 15, correlation: -1 }
  };

  const calcAvgRisk = (data: any[]) => {
    let total = 0;
    let weightSum = 0;

    for (const item of data) {
      const key = item.name.trim();
      const dayChange = item.day ?? 0;

      if (riskWeights[key]) {
        const { weight, correlation } = riskWeights[key];
        total += (weight / 100.0) * correlation * dayChange;
        weightSum += weight / 100.0;
      }
    }

    return weightSum > 0 ? total / weightSum : 0;
  };

  const fetchCalendar = async () => {
    try {
      const response = await fetch(`/api/calendar`);
      const data = await response.json();

      if (response.ok && data?.data) {
        const grouped: Record<string, any[]> = {};

        const adjustedData = data.data.map((item: any) => {
          let localDate = null;
          let timeKey = item.time;

          if (item.time && item.time.includes(":")) {
            const [hour, minute] = item.time.split(":").map(Number);
            const utcDate = new Date();
            utcDate.setUTCHours(hour, minute, 0, 0);

            localDate = new Date(utcDate);
            timeKey = localDate.toTimeString().slice(0, 5);

            if ((item.signalFX === null || item.signalEQ === null) && item.previous) {
              if (!grouped[timeKey]) grouped[timeKey] = [];
              grouped[timeKey].push({ ...item, localDate });
            }
          }

          return {
            ...item,
            time: timeKey,
            localDate
          };
        });

        if (calendarIDs.length === 0) {
          calendarIDs = data.data.map((item: any) => item.id.toString());
          calendarIDs.forEach((id) => {
            if (sock && sock.readyState === SockJS.OPEN) {
              sock.send(
                JSON.stringify({ _event: "subscribe", tzID: TimeZoneID, message: `event-${id}:` })
              );
            }
          });
        }

        calendarData = adjustedData;
        clearAllCalendarTimers();
        setupEventCountdowns(grouped);
      }
    } catch (err) {
      console.error("Error fetching calendar data:", err);
    }
  };

  function setupEventCountdowns(groupedEvents: Record<string, any[]>) {
    const delays = [
      { label: "EVENT +5 SEC", delay: 5000 },
      { label: "EVENT +15 SEC", delay: 15000 },
      { label: "EVENT +30 SEC", delay: 30000 },
      { label: "EVENT +1 MIN", delay: 60000 },
      { label: "EVENT +2 MIN", delay: 120000 },
      { label: "EVENT +5 MIN (END)", delay: 300000 }
    ];

    const now = new Date();

    for (const [timeKey, events] of Object.entries(groupedEvents)) {
      const targetTime = events[0].localDate;

      if (!targetTime) continue;

      const elapsed = now.getTime() - targetTime.getTime();

      calendarTimers[timeKey] = [];

      for (const { label, delay } of delays) {
        const timeUntilTrigger = delay - elapsed;

        if (timeUntilTrigger >= 0) {
          const timeout = setTimeout(() => {
            fetchCalendar();
          }, timeUntilTrigger);
          calendarTimers[timeKey].push(timeout);
        }
      }
    }
  }

  function clearAllCalendarTimers() {
    for (const key in calendarTimers) {
      for (const timer of calendarTimers[key]) {
        clearTimeout(timer);
      }
    }
    calendarTimers = {};
  }

  const fetchEarnings = async () => {
    try {
      const response = await fetch(`/api/earnings`);
      const data = await response.json();

      if (response.ok && data?.data) {
        earningsData = data.data;
        earningsIDs = data.data.map((item: any) => item.id.toString());
        earningsIDs.forEach((id) => {
          if (sock && sock.readyState === SockJS.OPEN) {
            sock.send(
              JSON.stringify({
                _event: "subscribe",
                tzID: TimeZoneID,
                message: `EarningsCal-${id}:`
              })
            );
          }
        });
      }
    } catch (err) {
      console.error("Error fetching earnings data:", err);
    }
  };

  const fetchCot = async () => {
    const updatedCotData = await Promise.all(
      cotData.map(async (item) => {
        try {
          const res = await fetch(`/api/cot-single?id=${encodeURIComponent(item.id)}`);
          const data = await res.json();

          if (res.ok && data?.filterData?.length > 0) {
            const latest = data.filterData[0];
            cotDate = latest.date;

            return {
              ...item,
              net: latest.net,
              changeNet: latest.changeNet
            };
          }
        } catch (err) {
          console.error(`Failed to fetch COT for ${item.name}:`, err);
        }
        return item;
      })
    );

    cotData = updatedCotData;
  };

  const normalizeIndicator = (actual: any, lowest: any, highest: any, correlation: any) => {
    if (
      actual === null ||
      highest === null ||
      lowest === null ||
      isNaN(actual) ||
      isNaN(highest) ||
      isNaN(lowest) ||
      highest === lowest
    )
      return 0.5;
    const raw = (actual - lowest) / (highest - lowest);
    return correlation === -1 ? 1 - raw : raw;
  };

  const calcCurrencyScore = (indicator: any) => {
    const normalized = normalizeIndicator(
      indicator.actual,
      indicator.lowest,
      indicator.highest,
      indicator.CurrencyCorrelation
    );
    return normalized * indicator.CurrencyWeight;
  };

  const calcEquityScore = (indicator: any) => {
    const normalized = normalizeIndicator(
      indicator.actual,
      indicator.lowest,
      indicator.highest,
      indicator.EquityCorrelation
    );
    return normalized * indicator.EquityWeight;
  };

  const fetchCountry = async (country: any) => {
    try {
      const res = await fetch(`/api/economic-indicators?id=${country.id}`);
      const data = await res.json();
      if (res.ok && data.data?.length > 0) {
        const incoming = data.data;

        country.indicators.forEach((indicator: any) => {
          const match = incoming.find((item: any) => item.name === indicator.name);
          if (match) {
            indicator.actual = match.actual;
            indicator.previous = match.previous;
            indicator.highest = match.highest;
            indicator.lowest = match.lowest;
            indicator.unit = match.unit;
            indicator.date = match.date;
            indicator.CurrencyScore = calcCurrencyScore(indicator);
            indicator.EquityScore = calcEquityScore(indicator);
          }
        });

        country.indicators.map((indicator: any) => {
          if (indicator.name === "Real Rate") {
            indicator.actual = parseFloat(
              (
                country.indicators.find((item: any) => item.name === "Interest Rate").actual -
                country.indicators.find((item: any) => item.name === "Inflation Expectations")
                  .actual
              ).toFixed(2)
            );
            indicator.previous = parseFloat(
              (
                country.indicators.find((item: any) => item.name === "Interest Rate").previous -
                country.indicators.find((item: any) => item.name === "Inflation Expectations")
                  .previous
              ).toFixed(2)
            );
            indicator.highest = parseFloat(
              (
                country.indicators.find((item: any) => item.name === "Interest Rate").highest -
                country.indicators.find((item: any) => item.name === "Inflation Expectations")
                  .highest
              ).toFixed(2)
            );
            indicator.lowest = parseFloat(
              (
                country.indicators.find((item: any) => item.name === "Interest Rate").lowest -
                country.indicators.find((item: any) => item.name === "Inflation Expectations")
                  .lowest
              ).toFixed(2)
            );
            indicator.unit = "percent";
            indicator.CurrencyScore = calcCurrencyScore(indicator);
            indicator.EquityScore = calcEquityScore(indicator);
          }
        });

        if (country.id === "united-states") {
          USIndicator = { ...country };
        } else if (country.id === "euro-area") {
          EUIndicator = { ...country };
        } else if (country.id === "united-kingdom") {
          GBIndicator = { ...country };
        } else if (country.id === "australia") {
          AUIndicator = { ...country };
        } else if (country.id === "canada") {
          CAIndicator = { ...country };
        } else if (country.id === "japan") {
          JPIndicator = { ...country };
        } else if (country.id === "switzerland") {
          CHIndicator = { ...country };
        }
      }
    } catch (err) {
      console.error(`Failed to fetch data for ${country.name}:`, err);
    }
  };

  const fetchAllCountries = async () => {
    const allCountries = [
      USDIndicator,
      EURIndicator,
      GBPIndicator,
      AUDIndicator,
      CADIndicator,
      JPYIndicator,
      CHFIndicator
    ];

    await Promise.all(allCountries.map((country) => fetchCountry(country)));
  };

  onMount(() => {
    updateProgressAndCountdown();
    interval = setInterval(updateProgressAndCountdown, 1000);

    fetchCot();
    fetchCalendar();
    fetchEarnings();
    fetchAllData();
    fetchAllCountries();

    afterNavigate((nav) => {
      if (nav.to?.url.pathname === "/market-risk") {
        if (typeof window !== "undefined" && (window as any).gtag) {
          (window as any).gtag("event", "conversion", {
            send_to: "AW-17106500203/OUjSCLyz-M4aEOv0gd0_"
          });
        }
      }
    });
  });

  onDestroy(() => {
    clearInterval(interval);
    clearAllCalendarTimers();

    if (sock && (sock.readyState === SockJS.OPEN || sock.readyState === SockJS.CONNECTING)) {
      sock.close(1000, "Component Unmounted");
    }
    stopHeartbeat();
    sock = null;
  });
</script>

<section class="mx-auto max-w-7xl space-y-5 px-2 py-5">
  <h1 class="text-center text-2xl leading-none font-bold tracking-tighter uppercase">
    Market Risk
  </h1>
  <div class="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
    <div class="space-y-2">
      <ListAverage average={USAvg} items={usData} title="Indices" />
      <h2
        class="rounded-md border-emerald-600/12 bg-emerald-600/12 px-2 py-2.5 text-sm leading-none font-medium tracking-tighter uppercase"
      >
        Equities
      </h2>
      <div class="grid grid-cols-1 gap-2 md:grid-cols-2">
        <ListAverage average={DJAvg} items={djData} title="D JONES" />
        <ListAverage average={NQAvg} items={nqData} title="NASDAQ" />
      </div>
    </div>

    <div class="space-y-2 xl:col-span-2">
      <div class="grid grid-cols-1 gap-2 xl:grid-cols-2">
        <ListAverage average={RISKAvg} items={riskData} title="Risk Indicators" />
        <ListCot items={cotData} title="COT Report" date={cotDate} />
      </div>
      <div class="hidden xl:block">
        <!-- <ListCalendar items={calendarData} title="Economic Calendar" /> -->
        <ListEarnings items={earningsData} {showEarnings} title="Earnings Calendar" />
      </div>
    </div>

    <div class="space-y-2">
      <h2
        class="rounded-md border-emerald-600/12 bg-emerald-600/12 px-2 py-2.5 text-sm leading-none font-medium tracking-tighter uppercase"
      >
        Risk Correlations
      </h2>
      <ListTable items={idxData} title="Indices" />
      <ListTable items={yxdData} title="Forex" />

      <ListSession {tokyoStatus} {londonStatus} {newyorkStatus} {nyseStatus} />
    </div>

    <div class="space-y-2 md:row-end-3 lg:col-span-full xl:hidden">
      <ListCalendar items={calendarData} title="Economic Calendar" />
    </div>
  </div>

  <div class="relative grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3">
    <ListIndicators
      icon={USIndicator.icon}
      title={USIndicator.name}
      items={USIndicator.indicators}
    />
    <ListIndicators
      icon={EUIndicator.icon}
      title={EUIndicator.name}
      items={EUIndicator.indicators}
    />
    <ListIndicators
      icon={GBIndicator.icon}
      title={GBIndicator.name}
      items={GBIndicator.indicators}
    />
    <ListIndicators
      icon={AUIndicator.icon}
      title={AUIndicator.name}
      items={AUIndicator.indicators}
    />
    <ListIndicators
      icon={CAIndicator.icon}
      title={CAIndicator.name}
      items={CAIndicator.indicators}
    />
    <ListIndicators
      icon={JPIndicator.icon}
      title={JPIndicator.name}
      items={JPIndicator.indicators}
    />
    <ListIndicators
      icon={CHIndicator.icon}
      title={CHIndicator.name}
      items={CHIndicator.indicators}
    />

    <div
      class="absolute inset-x-0 top-0 -z-10 flex transform-gpu justify-center overflow-hidden blur-3xl"
      aria-hidden="true"
    >
      <div
        class="aspect-[1108/632] w-[69.25rem] flex-none bg-gradient-to-r from-emerald-500 to-blue-600 opacity-25"
        style="clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%)"
      ></div>
    </div>
  </div>
</section>
