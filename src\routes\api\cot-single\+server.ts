import { json } from "@sveltejs/kit";
import { getDynamicTTL } from "$lib/utils";
import { services } from "$lib/server/services";
import type { RequestHandler } from "@sveltejs/kit";

export const GET: RequestHandler = async ({ url }) => {
  const id = url.searchParams.get("id");

  if (!id) {
    return json({ error: "Missing id" }, { status: 400 });
  }

  const redis = services.redis();
  const cacheKey = `cot:${id}`;
  const cached = await redis.get(cacheKey);

  if (cached) {
    const parsed = typeof cached === "string" ? JSON.parse(cached) : cached;
    return json({ filterData: parsed }, { status: 200 });
  } else {
    try {
      const query = new URLSearchParams({
        $limit: "1",
        $offset: "0",
        $order: "report_date_as_yyyy_mm_dd desc",
        $$app_token: "UMfq3Fy78WORHlC75rBdDEa8x",
        $where: `cftc_contract_market_code='${id}'`,
        $select:
          "cftc_contract_market_code,report_date_as_yyyy_mm_dd,noncomm_positions_long_all,noncomm_positions_short_all,change_in_noncomm_long_all,change_in_noncomm_short_all"
      }).toString();

      const response = await fetch(
        `https://publicreporting.cftc.gov/resource/6dca-aqww.json?${query}`
      );
      const data = await response.json();

      if (!response.ok || !data) {
        return json({ error: "Failed to fetch COT data" }, { status: 400 });
      }

      const filterData = data.map((item: { [key: string]: string }) => ({
        date: item.report_date_as_yyyy_mm_dd,
        net: parseInt(item.noncomm_positions_long_all) - parseInt(item.noncomm_positions_short_all),
        long: parseInt(item.noncomm_positions_long_all),
        short: parseInt(item.noncomm_positions_short_all),
        changeNet:
          parseInt(item.change_in_noncomm_long_all) - parseInt(item.change_in_noncomm_short_all),
        changeLong: parseInt(item.change_in_noncomm_long_all),
        changeShort: parseInt(item.change_in_noncomm_short_all)
      }));

      const ttl = getDynamicTTL("FRIDAY");
      await redis.setex(cacheKey, ttl, JSON.stringify(filterData));

      return json({ filterData }, { status: 200 });
    } catch (error) {
      return json({ error: error }, { status: 500 });
    }
  }
};
