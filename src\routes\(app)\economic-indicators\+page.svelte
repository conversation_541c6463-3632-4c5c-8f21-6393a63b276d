<script lang="ts">
  import { onMount } from "svelte";
  import {
    USDIndicator,
    EURIndicator,
    GBPIndicator,
    AUDIndicator,
    CADIndicator,
    JPYIndicator,
    CHFIndicator
  } from "$lib/constants/economic-indicators";
  import ListIndicators from "$lib/components/app/list-indicators.svelte";

  let USIndicator = $state(USDIndicator);
  let EUIndicator = $state(EURIndicator);
  let GBIndicator = $state(GBPIndicator);
  let AUIndicator = $state(AUDIndicator);
  let CAIndicator = $state(CADIndicator);
  let JPIndicator = $state(JPYIndicator);
  let CHIndicator = $state(CHFIndicator);

  const normalizeIndicator = (actual: any, lowest: any, highest: any, correlation: any) => {
    if (
      actual === null ||
      highest === null ||
      lowest === null ||
      isNaN(actual) ||
      isNaN(highest) ||
      isNaN(lowest) ||
      highest === lowest
    )
      return 0.5;
    const raw = (actual - lowest) / (highest - lowest);
    return correlation === -1 ? 1 - raw : raw;
  };

  const calcCurrencyScore = (indicator: any) => {
    const normalized = normalizeIndicator(
      indicator.actual,
      indicator.lowest,
      indicator.highest,
      indicator.CurrencyCorrelation
    );
    return normalized * indicator.CurrencyWeight;
  };

  const calcEquityScore = (indicator: any) => {
    const normalized = normalizeIndicator(
      indicator.actual,
      indicator.lowest,
      indicator.highest,
      indicator.EquityCorrelation
    );
    return normalized * indicator.EquityWeight;
  };

  const fetchCountry = async (country: any) => {
    try {
      const res = await fetch(`/api/economic-indicators?id=${country.id}`);
      const data = await res.json();
      if (res.ok && data.data?.length > 0) {
        const incoming = data.data;

        country.indicators.forEach((indicator: any) => {
          const match = incoming.find((item: any) => item.name === indicator.name);
          if (match) {
            indicator.actual = match.actual;
            indicator.previous = match.previous;
            indicator.highest = match.highest;
            indicator.lowest = match.lowest;
            indicator.unit = match.unit;
            indicator.date = match.date;
            indicator.CurrencyScore = calcCurrencyScore(indicator);
            indicator.EquityScore = calcEquityScore(indicator);
          }
        });

        country.indicators.map((indicator: any) => {
          if (indicator.name === "Real Rate") {
            indicator.actual = parseFloat(
              (
                country.indicators.find((item: any) => item.name === "Interest Rate").actual -
                country.indicators.find((item: any) => item.name === "Inflation Expectations")
                  .actual
              ).toFixed(2)
            );
            indicator.previous = parseFloat(
              (
                country.indicators.find((item: any) => item.name === "Interest Rate").previous -
                country.indicators.find((item: any) => item.name === "Inflation Expectations")
                  .previous
              ).toFixed(2)
            );
            indicator.highest = parseFloat(
              (
                country.indicators.find((item: any) => item.name === "Interest Rate").highest -
                country.indicators.find((item: any) => item.name === "Inflation Expectations")
                  .highest
              ).toFixed(2)
            );
            indicator.lowest = parseFloat(
              (
                country.indicators.find((item: any) => item.name === "Interest Rate").lowest -
                country.indicators.find((item: any) => item.name === "Inflation Expectations")
                  .lowest
              ).toFixed(2)
            );
            indicator.unit = "percent";
            indicator.CurrencyScore = calcCurrencyScore(indicator);
            indicator.EquityScore = calcEquityScore(indicator);
          }
        });

        if (country.id === "united-states") {
          USIndicator = { ...country };
        } else if (country.id === "euro-area") {
          EUIndicator = { ...country };
        } else if (country.id === "united-kingdom") {
          GBIndicator = { ...country };
        } else if (country.id === "australia") {
          AUIndicator = { ...country };
        } else if (country.id === "canada") {
          CAIndicator = { ...country };
        } else if (country.id === "japan") {
          JPIndicator = { ...country };
        } else if (country.id === "switzerland") {
          CHIndicator = { ...country };
        }
      }
    } catch (err) {
      console.error(`Failed to fetch data for ${country.name}:`, err);
    }
  };

  const fetchAllCountries = async () => {
    const allCountries = [
      USDIndicator,
      EURIndicator,
      GBPIndicator,
      AUDIndicator,
      CADIndicator,
      JPYIndicator,
      CHFIndicator
    ];

    await Promise.all(allCountries.map((country) => fetchCountry(country)));
  };

  onMount(() => {
    fetchAllCountries();
  });
</script>

<section class="mx-auto max-w-7xl space-y-5 px-2 py-5">
  <h1 class="text-center text-2xl leading-none font-bold tracking-tighter uppercase">
    Economic Indicators
  </h1>
  <div class="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3">
    <ListIndicators
      icon={USIndicator.icon}
      title={USIndicator.name}
      items={USIndicator.indicators}
    />
    <ListIndicators
      icon={EUIndicator.icon}
      title={EUIndicator.name}
      items={EUIndicator.indicators}
    />
    <ListIndicators
      icon={GBIndicator.icon}
      title={GBIndicator.name}
      items={GBIndicator.indicators}
    />
    <ListIndicators
      icon={AUIndicator.icon}
      title={AUIndicator.name}
      items={AUIndicator.indicators}
    />
    <ListIndicators
      icon={CAIndicator.icon}
      title={CAIndicator.name}
      items={CAIndicator.indicators}
    />
    <ListIndicators
      icon={JPIndicator.icon}
      title={JPIndicator.name}
      items={JPIndicator.indicators}
    />
    <ListIndicators
      icon={CHIndicator.icon}
      title={CHIndicator.name}
      items={CHIndicator.indicators}
    />
  </div>
</section>
