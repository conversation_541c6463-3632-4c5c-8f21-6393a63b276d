<script lang="ts">
  import { enhance } from "$app/forms";
  import { Mail, Check, X } from "lucide-svelte";
  import { SocialLinks } from "$lib/constants/links";

  let { form } = $props();
  let isLoadingForm = $state(false);
  let recaptchaInput: HTMLInputElement;

  function validateForm(): boolean {
    const formElement = recaptchaInput?.closest("form") as HTMLFormElement;
    if (!formElement) return false;

    if (!formElement.checkValidity()) {
      formElement.reportValidity();
      return false;
    }

    return true;
  }

  async function handleRecaptcha() {
    const isValid = validateForm();

    if (!isValid) {
      isLoadingForm = false;
      return;
    }

    isLoadingForm = true;

    try {
      await (window as any).grecaptcha.enterprise.ready(async () => {
        const token = await (window as any).grecaptcha.enterprise.execute(
          "6Lf3UiQrAAAAAOI328aI1jf_-Ak6XN8druAVbqYH",
          { action: "submit" }
        );

        if (recaptchaInput) {
          recaptchaInput.value = token;
        }

        const formElement = recaptchaInput?.closest("form");

        if (formElement) {
          formElement.requestSubmit();
        }
      });
    } catch (error) {
      console.error(error);
      isLoadingForm = false;
    }
  }

  async function handleSubmit() {
    isLoadingForm = true;

    return async ({ update, result }: { update: () => Promise<void>; result: any }) => {
      await update();

      isLoadingForm = false;
    };
  }
</script>

<div class="relative isolate">
  <div class="mx-auto grid max-w-7xl grid-cols-1 lg:grid-cols-2">
    <div class="relative px-4 pt-14 pb-10 sm:pt-22 lg:static lg:px-8 lg:py-38">
      <div class="mx-auto max-w-xl lg:mx-0 lg:max-w-lg">
        <div
          class="absolute inset-y-0 left-0 -z-10 w-full overflow-hidden ring-1 ring-white/5 lg:w-1/2"
        >
          <svg
            class="absolute inset-0 h-full w-full [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)] stroke-gray-700"
            aria-hidden="true"
          >
            <defs>
              <pattern
                id="54f88622-e7f8-4f1d-aaf9-c2f5e46dd1f2"
                width="200"
                height="200"
                x="100%"
                y="-1"
                patternUnits="userSpaceOnUse"
              >
                <path d="M130 200V.5M.5 .5H200" fill="none" />
              </pattern>
            </defs>
            <svg x="100%" y="-1" class="overflow-visible fill-gray-800/20">
              <path d="M-470.5 0h201v201h-201Z" stroke-width="0" />
            </svg>
            <rect
              width="100%"
              height="100%"
              stroke-width="0"
              fill="url(#54f88622-e7f8-4f1d-aaf9-c2f5e46dd1f2)"
            />
          </svg>
          <div
            class="absolute top-[calc(100%-13rem)] -left-56 transform-gpu blur-3xl lg:top-[calc(50%-7rem)] lg:left-[max(-14rem,calc(100%-59rem))]"
            aria-hidden="true"
          >
            <div
              class="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-br from-emerald-500 to-blue-600 opacity-20"
              style="clip-path: polygon(74.1% 56.1%, 100% 38.6%, 97.5% 73.3%, 85.5% 100%, 80.7% 98.2%, 72.5% 67.7%, 60.2% 37.8%, 52.4% 32.2%, 47.5% 41.9%, 45.2% 65.8%, 27.5% 23.5%, 0.1% 35.4%, 17.9% 0.1%, 27.6% 23.5%, 76.1% 2.6%, 74.1% 56.1%)"
            ></div>
          </div>
        </div>
        <h2 class="text-4xl leading-none font-bold tracking-tighter">Contact Us</h2>
        <p class="mt-2 text-lg leading-8 text-gray-300">
          Have a question or need support? <br />Contact us and we will be happy to help.
        </p>
        <dl class="mt-6 space-y-4 text-base leading-7 text-gray-300">
          {#each SocialLinks as item}
            {@const Icon = item.icon}
            <div class="flex gap-x-2">
              <dt class="flex-none">
                <Icon class="h-7 w-6 text-gray-400 select-none" />
              </dt>
              <dd>
                <a href={item.route} target="_blank" class="hover:text-white">{item.label}</a>
              </dd>
            </div>
          {/each}
        </dl>
      </div>
    </div>
    <form
      method="POST"
      action="?/submit"
      use:enhance={handleSubmit}
      class="px-4 pt-10 pb-10 lg:px-8 lg:py-28"
    >
      <input type="hidden" name="recaptcha_token" bind:this={recaptchaInput} />
      <div class="mx-auto max-w-xl lg:mr-0 lg:max-w-lg">
        <div class="grid grid-cols-1 gap-x-8 gap-y-6 sm:grid-cols-2">
          <div>
            <label for="firstname" class="block text-sm leading-6 font-semibold text-white"
              >First name</label
            >
            <div class="mt-1">
              <input
                type="text"
                id="firstname"
                name="firstname"
                autocomplete="given-name"
                class="block w-full rounded-md border-0 bg-white/5 px-3.5 py-2 text-white shadow-sm ring-1 ring-white/10 ring-inset focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-500 sm:text-sm sm:leading-6"
                required
              />
            </div>
          </div>
          <div>
            <label for="lastname" class="block text-sm leading-6 font-semibold text-white"
              >Last name</label
            >
            <div class="mt-1">
              <input
                type="text"
                id="lastname"
                name="lastname"
                autocomplete="family-name"
                class="block w-full rounded-md border-0 bg-white/5 px-3.5 py-2 text-white shadow-sm ring-1 ring-white/10 ring-inset focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-500 sm:text-sm sm:leading-6"
                required
              />
            </div>
          </div>
          <div class="sm:col-span-2">
            <label for="email" class="block text-sm leading-6 font-semibold text-white">Email</label
            >
            <div class="mt-1">
              <input
                type="email"
                id="email"
                name="email"
                autocomplete="email"
                class="block w-full rounded-md border-0 bg-white/5 px-3.5 py-2 text-white shadow-sm ring-1 ring-white/10 ring-inset focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-500 sm:text-sm sm:leading-6"
                required
              />
            </div>
          </div>
          <div class="sm:col-span-2">
            <label for="message" class="block text-sm leading-6 font-semibold text-white"
              >Message</label
            >
            <div class="mt-1">
              <textarea
                id="message"
                name="message"
                class="block w-full rounded-md border-0 bg-white/5 px-3.5 py-2 text-white shadow-sm ring-1 ring-white/10 ring-inset focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-500 sm:text-sm sm:leading-6"
                minlength="10"
                required
                rows="4"
              ></textarea>
            </div>
          </div>
        </div>
        <div class="mt-8 flex justify-center">
          <button
            type="button"
            disabled={isLoadingForm}
            onclick={handleRecaptcha}
            class="inline-flex w-full cursor-pointer items-center justify-center gap-x-1 rounded-md border border-emerald-600 bg-emerald-600 px-3.5 py-2.5 text-center text-sm font-medium tracking-tighter text-white shadow-sm hover:bg-emerald-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-500 disabled:pointer-events-none disabled:opacity-50"
          >
            {#if isLoadingForm}
              <svg
                class="size-4 animate-spin text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                ><circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle><path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path></svg
              > Sending…
            {:else}
              <Mail class="size-5 flex-shrink-0" /> Send Message
            {/if}
          </button>
        </div>
        <div class="mt-4">
          {#if form?.error}
            <p
              class="flex items-center justify-center gap-1 rounded-md bg-red-500 py-2 text-center text-sm leading-none tracking-tighter text-white"
            >
              <X class="size-6" />
              {form.error}
            </p>
          {:else if form?.success}
            <p
              class="flex items-center justify-center gap-1 rounded-md bg-emerald-500 py-2 text-center text-sm leading-none tracking-tighter text-white"
            >
              <Check class="size-6" />
              {form.text}
            </p>
          {/if}
        </div>
      </div>
    </form>
  </div>
</div>
