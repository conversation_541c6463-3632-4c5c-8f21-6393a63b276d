<script lang="ts">
	import { onMount } from "svelte";

	let time = $state(new Date());

	let hours = $derived(time.getHours());
	let minutes = $derived(time.getMinutes());
	let seconds = $derived(time.getSeconds());

	onMount(() => {
	  const interval = setInterval(() => {
	    time = new Date();
	  }, 1000);

	  return () => {
	    clearInterval(interval);
	  };
	});
</script>

<div class="w-full tracking-tighter">
  <div class="w-full flex justify-between gap-2 px-2 py-2.5 rounded-md border-white/5 bg-white/5">
    <h2 class="text-sm font-medium leading-none uppercase">Clock</h2>
  </div>
  <div class="px-2 py-1 text-5xl font-light leading-none -tracking-tighter uppercase">{String(hours).padStart(2, "0")}:{String(minutes).padStart(2, "0")}:{String(seconds).padStart(2, "0")}</div>
</div>
