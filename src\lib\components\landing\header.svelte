<script lang="ts">
  import { onMount } from "svelte";
  import { page } from "$app/state";
  import { goto } from "$app/navigation";
  import { authClient } from "$lib/auth-client";
  import { HeaderLinks, UserLinksApp } from "$lib/constants/links";
  import { ArrowRight, User, LayoutDashboard } from "lucide-svelte";

  let props = $props();
  let user = $state(props?.user);
  let subscription = $state(props?.subscription);
  let showMenu = $state(false);
  let showProfile = $state(false);
  let pathUrl = $derived(page.url.pathname);
  let headerElement: HTMLElement;

  async function handleLogout() {
    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          user = null;
          subscription = null;
          showMenu = false;
          showProfile = false;
        }
      }
    });
  }

  $effect(() => {
    pathUrl;
    showMenu = false;
    showProfile = false;

    user = props.user;
    subscription = props.subscription;
  });

  onMount(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showProfile && headerElement && !headerElement.contains(event.target as Node)) {
        showProfile = false;
      }
    };

    const handleKeydown = (event: { key: string }) => {
      if (event.key === "Escape") {
        pathUrl;
        showMenu = false;
        showProfile = false;
      }
    };

    window.addEventListener("keydown", handleKeydown);
    window.addEventListener("mousedown", handleClickOutside);

    return () => {
      window.removeEventListener("keydown", handleKeydown);
      window.removeEventListener("mousedown", handleClickOutside);
    };
  });

  const handleNavClick = async (e: MouseEvent, route: string) => {
    showMenu = false;
    showProfile = false;

    const currentPath = page.url.pathname;

    if (route.startsWith("#")) {
      e.preventDefault();
      const targetId = route.slice(1);
      const el = document.getElementById(targetId);

      if (el) {
        el.scrollIntoView({ behavior: "smooth", block: "start" });
      } else if (currentPath !== "/") {
        await goto("/");

        setTimeout(() => {
          const targetEl = document.getElementById(targetId);
          if (targetEl) {
            targetEl.scrollIntoView({ behavior: "smooth", block: "start" });
          }
        }, 30);
      }
    } else if (route === "/") {
      e.preventDefault();

      if (currentPath !== "/") {
        await goto("/");
      } else {
        window.scrollTo({ top: 0, behavior: "smooth" });
      }
    }
  };
</script>

<header
  bind:this={headerElement}
  class="fixed top-0 left-0 z-50 w-full bg-transparent py-3.5 backdrop-blur-lg {showMenu
    ? `h-full overflow-y-auto`
    : ``}"
>
  <div class="mx-auto w-full max-w-7xl px-4 lg:px-8">
    <div class="flex w-full items-center justify-between">
      <div>
        <a href={HeaderLinks[0].route}>
          <img src="/logo.svg" class="-mt-3 w-[190px] select-none" alt="Macro Edge" />
        </a>
      </div>

      <div class="hidden lg:block">
        <div class="flex items-center space-x-1.5">
          {#each HeaderLinks as item}
            {@const Icon = item.icon}
            <a
              href={item.route}
              class="rounded-md p-2 text-sm hover:bg-white/8"
              aria-current="page"
              onclick={(e) => handleNavClick(e, item.route)}
            >
              <div class="flex items-center gap-2">
                <Icon class="block size-5 select-none lg:hidden xl:block" />
                <span>{item.label}</span>
              </div>
            </a>
          {/each}
        </div>
      </div>

      <div class="hidden lg:block">
        <div class="flex items-center space-x-1.5">
          {#if user}
            <a
              href="/market-risk"
              class="inline-flex w-full items-center justify-center gap-x-1 rounded-lg border border-transparent bg-emerald-600 px-4 py-2 text-sm font-medium tracking-tighter hover:bg-emerald-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-500 disabled:pointer-events-none disabled:opacity-50 sm:w-auto"
            >
              <LayoutDashboard class="size-5 flex-shrink-0" />Dashboard</a
            >
            <!-- <p
              class="rounded-md px-2 py-1 text-center text-xs leading-none font-medium uppercase ring-1 ring-inset {subscription?.status ===
              'trialing'
                ? `bg-yellow-500/20 text-yellow-500 ring-yellow-500/20`
                : subscription?.status === 'active'
                  ? `bg-emerald-500/20 text-emerald-500 ring-emerald-500/20`
                  : `bg-red-500/20 text-red-500 ring-red-500/20`}"
            >
              {subscription?.status === "trialing"
                ? "Trial"
                : subscription?.status === "active"
                  ? "Active"
                  : "Cancelled"}
            </p> -->
            <div class="relative">
              <div>
                <button
                  type="button"
                  class="relative flex size-8 cursor-pointer items-center rounded-full bg-gray-800 text-sm focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800 focus:outline-hidden"
                  id="user-menu-button"
                  aria-expanded="false"
                  aria-haspopup="true"
                  onclick={() => (showProfile = !showProfile)}
                >
                  <span class="absolute -inset-1.5"></span>
                  <span class="sr-only">Open user menu</span>
                  <img
                    class="size-8 rounded-full select-none"
                    src={user?.image || "/user.png"}
                    alt={user?.name}
                  />
                </button>
              </div>

              <div
                class="absolute right-0 z-10 mt-2 w-48 origin-top-right overflow-hidden rounded-sm bg-gradient-to-r from-gray-800 to-gray-900 shadow-lg focus:outline-hidden {showProfile
                  ? `block`
                  : `hidden`}"
                role="menu"
                aria-orientation="vertical"
                aria-labelledby="user-menu-button"
                tabindex="-1"
              >
                {#each UserLinksApp as item}
                  {@const Icon = item.icon}
                  {#if item.label === `Logout`}
                    <button
                      type="button"
                      class="block w-full cursor-pointer px-4 py-2 text-sm hover:bg-white/5"
                      role="menuitem"
                      tabindex="-1"
                      id="user-menu-item-0"
                      onclick={() => handleLogout()}
                    >
                      <div class="flex items-center gap-2 text-red-500 hover:text-red-500">
                        <Icon class="size-5 select-none" />
                        <span>{item.label}</span>
                      </div>
                    </button>
                  {:else}
                    <a
                      href={item.route}
                      class="block px-4 py-2 text-sm hover:bg-white/5 {pathUrl.startsWith(
                        item.route
                      )
                        ? `bg-white/5`
                        : ``}"
                      role="menuitem"
                      tabindex="-1"
                      id="user-menu-item-0"
                    >
                      <div class="flex items-center gap-2">
                        <Icon class="size-5 select-none" />
                        <span>{item.label}</span>
                      </div>
                    </a>
                  {/if}
                {/each}
              </div>
            </div>
          {:else}
            <a
              href="/login"
              class="focus-visible:outline-inset-2 inline-flex w-full items-center justify-center gap-x-1 rounded-lg border border-transparent px-3 py-2 text-sm font-medium tracking-tighter hover:bg-white/8 focus-visible:outline-2 focus-visible:outline-white disabled:pointer-events-none disabled:opacity-50 sm:w-auto"
              ><User class="size-5 flex-shrink-0" /> Login</a
            >
            <a
              href="/login"
              class="inline-flex w-full items-center justify-center gap-x-1 rounded-lg border border-transparent bg-emerald-600 px-3 py-2 text-sm font-medium tracking-tighter hover:bg-emerald-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-500 disabled:pointer-events-none disabled:opacity-50 sm:w-auto"
              >Start Free Trial <ArrowRight class="size-5 flex-shrink-0" /></a
            >
          {/if}
        </div>
      </div>

      <div class="flex gap-2 lg:hidden">
        {#if user}
          <a
            href="/market-risk"
            class="inline-flex items-center justify-center gap-x-1 rounded-lg border border-transparent bg-emerald-600 px-4 py-2 text-sm font-medium tracking-tighter hover:bg-emerald-500 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800 focus:outline-hidden disabled:pointer-events-none disabled:opacity-50"
          >
            <LayoutDashboard class="size-5 flex-shrink-0" />Dashboard</a
          >
        {/if}

        <button
          type="button"
          class="relative inline-flex cursor-pointer items-center justify-center rounded-md bg-white/5 p-2 hover:text-white focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800 focus:outline-hidden"
          aria-controls="mobile-menu"
          aria-expanded="false"
          onclick={() => (showMenu = !showMenu)}
        >
          <span class="absolute -inset-0.5"></span>
          <span class="sr-only">Open main menu</span>
          <svg
            class="size-6 {showMenu ? `hidden` : `block`}"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            aria-hidden="true"
            data-slot="icon"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
            />
          </svg>
          <svg
            class="size-6 {!showMenu ? `hidden` : `block`}"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            aria-hidden="true"
            data-slot="icon"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <div class="lg:hidden {showMenu ? `block` : `hidden`}" id="mobile-menu">
    <div class="space-y-1 px-2 pt-2 pb-3 sm:px-3">
      {#each HeaderLinks as item}
        {@const Icon = item.icon}
        <a
          href={item.route}
          class="block rounded-md px-3 py-2 hover:bg-white/5"
          onclick={(e) => {
            handleNavClick(e, item.route);
          }}
        >
          <div class="flex items-center gap-2">
            <Icon class="size-5 select-none" />
            <span>{item.label}</span>
          </div>
        </a>
      {/each}
    </div>

    <div class="border-t border-gray-700 pt-4 pb-3">
      {#if user}
        <div class="flex items-center px-5">
          <div class="shrink-0">
            <img
              class="size-10 rounded-full select-none"
              src={user?.image || "/user.png"}
              alt={user?.name}
            />
          </div>

          <div class="ml-3">
            <div class="text-base/5">{user?.name}</div>
            <div class="text-sm text-gray-400">{user?.email}</div>
          </div>
        </div>

        <div class="mt-3 space-y-1 px-2">
          {#each UserLinksApp as item}
            {@const Icon = item.icon}

            {#if item.label === `Logout`}
              <button
                type="button"
                class="block w-full cursor-pointer rounded-md px-3 py-2 hover:bg-white/5"
                onclick={() => handleLogout()}
              >
                <div class="flex items-center gap-2 text-red-500 hover:text-red-500">
                  <Icon class="size-5 select-none" />
                  <span>{item.label}</span>
                </div>
              </button>
            {:else}
              <a href={item.route} class="block rounded-md px-3 py-2 hover:bg-white/5">
                <div class="flex items-center gap-2">
                  <Icon class="size-5 select-none" />
                  <span>{item.label}</span>
                </div>
              </a>
            {/if}
          {/each}
        </div>
      {:else}
        <div class="flex items-center space-x-1.5 px-3.5">
          <a
            href="/login"
            class="inline-flex w-full items-center justify-center gap-x-1 rounded-lg border border-transparent bg-white/8 px-3 py-2 text-sm font-medium tracking-tighter hover:bg-white/15 disabled:pointer-events-none disabled:opacity-50 sm:w-auto lg:bg-transparent lg:hover:bg-white/5"
            ><User class="size-5 flex-shrink-0" /> Login</a
          >
          <a
            href="/login"
            class="inline-flex w-full items-center justify-center gap-x-1 rounded-lg border border-transparent bg-emerald-600 px-3 py-2 text-sm font-medium tracking-tighter hover:bg-emerald-500 disabled:pointer-events-none disabled:opacity-50 sm:w-auto"
            >Start Free Trial <ArrowRight class="size-5 flex-shrink-0" /></a
          >
        </div>
      {/if}
    </div>
  </div>
</header>
